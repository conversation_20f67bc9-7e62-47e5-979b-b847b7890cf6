export interface Device {
  id: number
  name: string
  ip_address: string
  mac_address?: string
  device_type: DeviceType
  vendor?: string
  model?: string
  description?: string
  
  // Location
  location?: string
  building?: string
  floor?: string
  room?: string
  
  // Network Configuration
  subnet?: string
  vlan_id?: number
  snmp_community?: string
  snmp_version?: string
  snmp_port?: number
  
  // Status and Monitoring
  status: DeviceStatus
  last_seen?: string
  uptime?: number
  cpu_usage?: number
  memory_usage?: number
  disk_usage?: number
  
  // Additional Data
  system_info?: Record<string, any>
  interfaces?: Record<string, any>
  custom_fields?: Record<string, any>
  
  // Computed
  health_score?: number
  
  // Timestamps
  created_at: string
  updated_at: string
  is_active: boolean
}

export enum DeviceType {
  SWITCH = 'switch',
  ROUTER = 'router',
  FIREWALL = 'firewall',
  ACCESS_POINT = 'access_point',
  SERVER = 'server',
  WORKSTATION = 'workstation',
  PRINTER = 'printer',
  OTHER = 'other'
}

export enum DeviceStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  WARNING = 'warning',
  CRITICAL = 'critical',
  UNKNOWN = 'unknown'
}

export interface DeviceStats {
  total_devices: number
  online_devices: number
  offline_devices: number
  warning_devices: number
  critical_devices: number
  device_types: Record<string, number>
  average_health_score: number
}

# CampusGuard Frontend Development Environment

# Application
VITE_APP_TITLE=CampusGuard (Dev)
VITE_APP_DESCRIPTION=AI-Powered Network Monitoring System

# API Configuration
VITE_API_BASE_URL=http://localhost:8000
VITE_API_PREFIX=/api/v1
VITE_WS_URL=ws://localhost:8000/api/v1/ws

# Development
VITE_DEV_MODE=true
VITE_MOCK_API=false

# Features
VITE_ENABLE_AI_CHAT=true
VITE_ENABLE_TOPOLOGY=true
VITE_ENABLE_MONITORING=true

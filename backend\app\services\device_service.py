"""
Device management service
"""
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timed<PERSON>ta
from sqlalchemy.orm import Session
from loguru import logger

from ..core.database import SessionLocal
from ..models.device import Device, DeviceStatus
from ..models.performance import PerformanceMetric
from ..models.alert import <PERSON><PERSON>, <PERSON>ertType, AlertSeverity, AlertStatus
from .snmp_service import <PERSON><PERSON><PERSON><PERSON>ollector
from .alert_service import AlertService


class DeviceService:
    """Device management and monitoring service"""
    
    def __init__(self):
        self.snmp_collector = SNMPCollector()
        self.alert_service = AlertService()
    
    async def discover_devices(self, ip_range: str) -> List[Dict[str, Any]]:
        """Discover devices in IP range using SNMP"""
        try:
            discovered_devices = []
            
            # Parse IP range (simple implementation for /24 networks)
            if ip_range.endswith('/24'):
                base_ip = ip_range.replace('/24', '')
                base_parts = base_ip.split('.')
                if len(base_parts) == 4:
                    base_network = '.'.join(base_parts[:3])
                    
                    # Test each IP in the range (1-254)
                    tasks = []
                    for i in range(1, 255):
                        ip = f"{base_network}.{i}"
                        tasks.append(self._test_device_discovery(ip))
                    
                    # Execute discovery tasks concurrently
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    for result in results:
                        if isinstance(result, dict) and result.get('responsive'):
                            discovered_devices.append(result)
            
            logger.info(f"Discovered {len(discovered_devices)} devices in range {ip_range}")
            return discovered_devices
            
        except Exception as e:
            logger.error(f"Error discovering devices in range {ip_range}: {e}")
            return []
    
    async def _test_device_discovery(self, ip_address: str) -> Dict[str, Any]:
        """Test single IP for SNMP responsiveness"""
        try:
            # Test SNMP connectivity
            is_responsive = await self.snmp_collector.test_connectivity(ip_address)
            
            if is_responsive:
                # Get basic device info
                system_info = await self.snmp_collector.collect_device_info(
                    type('Device', (), {
                        'ip_address': ip_address,
                        'snmp_community': 'public'
                    })()
                )
                
                return {
                    'ip_address': ip_address,
                    'responsive': True,
                    'system_info': system_info,
                    'discovered_at': datetime.now().isoformat()
                }
            else:
                return {
                    'ip_address': ip_address,
                    'responsive': False
                }
                
        except Exception as e:
            logger.debug(f"Discovery test failed for {ip_address}: {e}")
            return {
                'ip_address': ip_address,
                'responsive': False,
                'error': str(e)
            }
    
    async def monitor_device(self, device: Device, db: Session) -> Dict[str, Any]:
        """Monitor single device and update status"""
        try:
            monitoring_result = {
                'device_id': device.id,
                'ip_address': device.ip_address,
                'status': device.status,
                'timestamp': datetime.now().isoformat(),
                'metrics_collected': 0,
                'alerts_generated': 0
            }
            
            # Test connectivity
            is_online = await self.snmp_collector.test_connectivity(
                device.ip_address, 
                device.snmp_community
            )
            
            if is_online:
                # Device is online, collect metrics
                metrics = await self.snmp_collector.collect_performance_metrics(device)
                
                # Store metrics in database
                for metric_data in metrics:
                    metric = PerformanceMetric(**metric_data)
                    db.add(metric)
                
                # Update device status and metrics
                device.status = DeviceStatus.ONLINE
                device.last_seen = datetime.now().isoformat()
                
                # Update device resource usage from metrics
                for metric_data in metrics:
                    if metric_data['metric_name'] == 'cpu_usage':
                        device.cpu_usage = metric_data['metric_value']
                    elif metric_data['metric_name'] == 'memory_usage':
                        device.memory_usage = metric_data['metric_value']
                
                # Check for alerts
                alerts_generated = await self._check_device_alerts(device, metrics, db)
                
                monitoring_result.update({
                    'status': DeviceStatus.ONLINE,
                    'metrics_collected': len(metrics),
                    'alerts_generated': alerts_generated
                })
                
            else:
                # Device is offline
                previous_status = device.status
                device.status = DeviceStatus.OFFLINE
                
                # Generate offline alert if status changed
                if previous_status != DeviceStatus.OFFLINE:
                    await self.alert_service.create_alert(
                        title=f"Device {device.name} is offline",
                        description=f"Device {device.name} ({device.ip_address}) is not responding to SNMP requests",
                        alert_type=AlertType.DEVICE_DOWN,
                        severity=AlertSeverity.HIGH,
                        device_id=device.id,
                        device_ip=device.ip_address,
                        db=db
                    )
                    monitoring_result['alerts_generated'] = 1
                
                monitoring_result['status'] = DeviceStatus.OFFLINE
            
            # Update device in database
            db.commit()
            
            return monitoring_result
            
        except Exception as e:
            logger.error(f"Error monitoring device {device.ip_address}: {e}")
            db.rollback()
            return {
                'device_id': device.id,
                'ip_address': device.ip_address,
                'status': DeviceStatus.UNKNOWN,
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    async def _check_device_alerts(self, device: Device, metrics: List[Dict[str, Any]], db: Session) -> int:
        """Check device metrics for alert conditions"""
        try:
            alerts_generated = 0
            
            for metric_data in metrics:
                metric_name = metric_data['metric_name']
                metric_value = metric_data['metric_value']
                
                # Check CPU usage
                if metric_name == 'cpu_usage' and metric_value > 80:
                    await self.alert_service.create_alert(
                        title=f"High CPU usage on {device.name}",
                        description=f"CPU usage is {metric_value}% on device {device.name}",
                        alert_type=AlertType.HIGH_CPU,
                        severity=AlertSeverity.HIGH if metric_value > 90 else AlertSeverity.MEDIUM,
                        device_id=device.id,
                        device_ip=device.ip_address,
                        threshold_value="80%",
                        current_value=f"{metric_value}%",
                        db=db
                    )
                    alerts_generated += 1
                
                # Check memory usage
                elif metric_name == 'memory_usage' and metric_value > 85:
                    await self.alert_service.create_alert(
                        title=f"High memory usage on {device.name}",
                        description=f"Memory usage is {metric_value}% on device {device.name}",
                        alert_type=AlertType.HIGH_MEMORY,
                        severity=AlertSeverity.HIGH if metric_value > 95 else AlertSeverity.MEDIUM,
                        device_id=device.id,
                        device_ip=device.ip_address,
                        threshold_value="85%",
                        current_value=f"{metric_value}%",
                        db=db
                    )
                    alerts_generated += 1
                
                # Check interface status
                elif metric_name.startswith('interface_') and metric_name.endswith('_status'):
                    if metric_value == 2:  # Interface down
                        interface_info = metric_data.get('metadata', {})
                        interface_desc = interface_info.get('interface_description', 'Unknown')
                        
                        await self.alert_service.create_alert(
                            title=f"Interface down on {device.name}",
                            description=f"Interface {interface_desc} is down on device {device.name}",
                            alert_type=AlertType.INTERFACE_DOWN,
                            severity=AlertSeverity.MEDIUM,
                            device_id=device.id,
                            device_ip=device.ip_address,
                            metadata=interface_info,
                            db=db
                        )
                        alerts_generated += 1
            
            return alerts_generated
            
        except Exception as e:
            logger.error(f"Error checking device alerts for {device.ip_address}: {e}")
            return 0
    
    async def monitor_all_devices(self) -> Dict[str, Any]:
        """Monitor all active devices"""
        try:
            with SessionLocal() as db:
                # Get all active devices
                devices = db.query(Device).filter(Device.is_active == True).all()
                
                if not devices:
                    logger.info("No devices found for monitoring")
                    return {
                        'total_devices': 0,
                        'monitoring_results': [],
                        'timestamp': datetime.now().isoformat()
                    }
                
                # Monitor devices concurrently
                tasks = []
                for device in devices:
                    tasks.append(self.monitor_device(device, db))
                
                monitoring_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                successful_results = []
                for result in monitoring_results:
                    if isinstance(result, dict):
                        successful_results.append(result)
                    else:
                        logger.error(f"Monitoring task failed: {result}")
                
                # Calculate summary statistics
                total_metrics = sum(r.get('metrics_collected', 0) for r in successful_results)
                total_alerts = sum(r.get('alerts_generated', 0) for r in successful_results)
                
                summary = {
                    'total_devices': len(devices),
                    'devices_monitored': len(successful_results),
                    'total_metrics_collected': total_metrics,
                    'total_alerts_generated': total_alerts,
                    'monitoring_results': successful_results,
                    'timestamp': datetime.now().isoformat()
                }
                
                logger.info(f"Monitoring completed: {len(successful_results)}/{len(devices)} devices, {total_metrics} metrics, {total_alerts} alerts")
                return summary
                
        except Exception as e:
            logger.error(f"Error monitoring all devices: {e}")
            return {
                'total_devices': 0,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def get_device_health_summary(self, device_id: int) -> Dict[str, Any]:
        """Get comprehensive health summary for a device"""
        try:
            with SessionLocal() as db:
                device = db.query(Device).filter(
                    Device.id == device_id,
                    Device.is_active == True
                ).first()
                
                if not device:
                    return {'error': 'Device not found'}
                
                # Get recent metrics (last 24 hours)
                end_time = datetime.now()
                start_time = end_time - timedelta(hours=24)
                
                recent_metrics = db.query(PerformanceMetric).filter(
                    PerformanceMetric.device_id == device_id,
                    PerformanceMetric.timestamp >= start_time.isoformat(),
                    PerformanceMetric.timestamp <= end_time.isoformat(),
                    PerformanceMetric.is_active == True
                ).all()
                
                # Get recent alerts
                recent_alerts = db.query(Alert).filter(
                    Alert.device_id == device_id,
                    Alert.created_at >= start_time,
                    Alert.is_active == True
                ).all()
                
                # Calculate health metrics
                health_summary = {
                    'device_info': {
                        'id': device.id,
                        'name': device.name,
                        'ip_address': device.ip_address,
                        'device_type': device.device_type.value,
                        'status': device.status.value,
                        'last_seen': device.last_seen,
                        'health_score': device.health_score
                    },
                    'current_metrics': {
                        'cpu_usage': device.cpu_usage,
                        'memory_usage': device.memory_usage,
                        'disk_usage': device.disk_usage,
                        'uptime': device.uptime
                    },
                    'recent_activity': {
                        'metrics_count': len(recent_metrics),
                        'alerts_count': len(recent_alerts),
                        'active_alerts': len([a for a in recent_alerts if a.status in [AlertStatus.OPEN, AlertStatus.ACKNOWLEDGED]])
                    },
                    'timestamp': datetime.now().isoformat()
                }
                
                return health_summary
                
        except Exception as e:
            logger.error(f"Error getting device health summary for device {device_id}: {e}")
            return {'error': str(e)}

<template>
  <a-layout class="layout">
    <!-- Header -->
    <a-layout-header class="header">
      <div class="header-left">
        <a-button
          type="text"
          class="trigger"
          @click="appStore.toggleCollapsed"
        >
          <MenuUnfoldOutlined v-if="appStore.collapsed" />
          <MenuFoldOutlined v-else />
        </a-button>
        <h1 class="title">{{ appStore.title }}</h1>
      </div>
      
      <div class="header-right">
        <!-- WebSocket Status -->
        <a-badge 
          :status="wsStore.connected ? 'success' : 'error'" 
          :text="wsStore.connected ? '已连接' : '未连接'"
          class="ws-status"
        />
        
        <!-- Theme Toggle -->
        <a-button
          type="text"
          @click="appStore.toggleTheme"
          class="theme-toggle"
        >
          <SunOutlined v-if="appStore.isDark" />
          <MoonOutlined v-else />
        </a-button>
        
        <!-- User Menu -->
        <a-dropdown>
          <a-button type="text" class="user-menu">
            <UserOutlined />
            <span class="username">管理员</span>
            <DownOutlined />
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="profile">
                <UserOutlined />
                个人资料
              </a-menu-item>
              <a-menu-item key="settings">
                <SettingOutlined />
                系统设置
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="logout">
                <LogoutOutlined />
                退出登录
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </a-layout-header>

    <a-layout>
      <!-- Sidebar -->
      <a-layout-sider
        v-model:collapsed="appStore.collapsed"
        :width="200"
        :collapsed-width="80"
        class="sidebar"
        theme="dark"
      >
        <div class="logo">
          <img src="/logo.svg" alt="CampusGuard" v-if="!appStore.collapsed" />
          <img src="/logo-mini.svg" alt="CG" v-else />
        </div>
        
        <a-menu
          v-model:selectedKeys="selectedKeys"
          v-model:openKeys="openKeys"
          mode="inline"
          theme="dark"
          :inline-collapsed="appStore.collapsed"
          @click="handleMenuClick"
        >
          <a-menu-item key="/dashboard">
            <DashboardOutlined />
            <span>监控大屏</span>
          </a-menu-item>
          
          <a-menu-item key="/devices">
            <DesktopOutlined />
            <span>设备管理</span>
          </a-menu-item>
          
          <a-menu-item key="/alerts">
            <AlertOutlined />
            <span>告警管理</span>
          </a-menu-item>
          
          <a-menu-item key="/performance">
            <LineChartOutlined />
            <span>性能监控</span>
          </a-menu-item>
          
          <a-menu-item key="/topology">
            <ClusterOutlined />
            <span>网络拓扑</span>
          </a-menu-item>
          
          <a-sub-menu key="/ai">
            <template #icon>
              <RobotOutlined />
            </template>
            <template #title>AI助手</template>
            <a-menu-item key="/ai/chat">
              <MessageOutlined />
              <span>AI对话</span>
            </a-menu-item>
            <a-menu-item key="/ai/analysis">
              <ExperimentOutlined />
              <span>AI分析</span>
            </a-menu-item>
          </a-sub-menu>

          <a-sub-menu key="/security">
            <template #icon>
              <SafetyCertificateOutlined />
            </template>
            <template #title>网络安全</template>
            <a-menu-item key="/security">
              <SecurityScanOutlined />
              <span>安全总览</span>
            </a-menu-item>
            <a-menu-item key="/security/threat-intel">
              <SecurityScanOutlined />
              <span>威胁情报</span>
            </a-menu-item>
            <a-menu-item key="/security/ip-reputation">
              <SearchOutlined />
              <span>IP信誉查询</span>
            </a-menu-item>
            <a-menu-item key="/security/blacklist">
              <StopOutlined />
              <span>黑名单管理</span>
            </a-menu-item>
          </a-sub-menu>

          <a-sub-menu key="/monitoring">
            <template #icon>
              <MonitorOutlined />
            </template>
            <template #title>监控管理</template>
            <a-menu-item key="/monitoring/control">
              <ControlOutlined />
              <span>监控控制台</span>
            </a-menu-item>
            <a-menu-item key="/monitoring/discovery">
              <SearchOutlined />
              <span>设备发现</span>
            </a-menu-item>
          </a-sub-menu>

          <a-menu-item key="/settings">
            <SettingOutlined />
            <span>系统设置</span>
          </a-menu-item>
        </a-menu>
      </a-layout-sider>

      <!-- Content -->
      <a-layout-content class="content">
        <router-view v-slot="{ Component }">
          <keep-alive>
            <component :is="Component" v-if="$route.meta.keepAlive" />
          </keep-alive>
          <component :is="Component" v-if="!$route.meta.keepAlive" />
        </router-view>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useWebSocketStore } from '@/stores/websocket'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  DesktopOutlined,
  AlertOutlined,
  LineChartOutlined,
  ClusterOutlined,
  RobotOutlined,
  MessageOutlined,
  ExperimentOutlined,
  SafetyCertificateOutlined,
  SecurityScanOutlined,
  SearchOutlined,
  StopOutlined,
  MonitorOutlined,
  ControlOutlined,
  SettingOutlined,
  UserOutlined,
  DownOutlined,
  LogoutOutlined,
  SunOutlined,
  MoonOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const wsStore = useWebSocketStore()

// Menu state
const selectedKeys = ref<string[]>([])
const openKeys = ref<string[]>([])

// Computed
const currentPath = computed(() => route.path)

// Watch route changes
watch(currentPath, (newPath) => {
  selectedKeys.value = [newPath]

  // Set open keys for sub menus
  if (newPath.startsWith('/ai')) {
    openKeys.value = ['/ai']
  } else if (newPath.startsWith('/security')) {
    openKeys.value = ['/security']
  } else if (newPath.startsWith('/monitoring')) {
    openKeys.value = ['/monitoring']
  }
}, { immediate: true })

// Methods
const handleMenuClick = ({ key }: { key: string }) => {
  router.push(key)
}

// Lifecycle
onMounted(() => {
  // Connect WebSocket
  wsStore.connect()

  // Set initial menu state
  selectedKeys.value = [route.path]
  if (route.path.startsWith('/ai')) {
    openKeys.value = ['/ai']
  } else if (route.path.startsWith('/security')) {
    openKeys.value = ['/security']
  } else if (route.path.startsWith('/monitoring')) {
    openKeys.value = ['/monitoring']
  }
})
</script>

<style scoped>
.layout {
  height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background: var(--header-bg);
  border-bottom: 1px solid var(--border-color);
  z-index: var(--z-header);
}

.header-left {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 18px;
  color: var(--text-white);
  margin-right: 16px;
}

.title {
  color: var(--text-white);
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.ws-status {
  color: var(--text-white);
}

.theme-toggle {
  color: var(--text-white);
  font-size: 16px;
}

.user-menu {
  color: var(--text-white);
  display: flex;
  align-items: center;
  gap: 8px;
}

.username {
  font-size: 14px;
}

.sidebar {
  background: var(--sidebar-bg);
  z-index: var(--z-sidebar);
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 6px;
}

.logo img {
  height: 32px;
}

.content {
  background: var(--bg-color);
  overflow: auto;
}

/* Dark theme adjustments */
[data-theme='dark'] .header {
  background: var(--header-bg);
  border-bottom-color: var(--border-color);
}

[data-theme='dark'] .sidebar {
  background: var(--sidebar-bg);
}
</style>

"""
Alert and notification models
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, Integer, Text, JSON, Enum, Foreign<PERSON>ey, <PERSON>olean
from sqlalchemy.orm import relationship
import enum

from .base import BaseModel


class AlertSeverity(str, enum.Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(str, enum.Enum):
    """Alert status"""
    OPEN = "open"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    CLOSED = "closed"


class AlertType(str, enum.Enum):
    """Alert types"""
    DEVICE_DOWN = "device_down"
    HIGH_CPU = "high_cpu"
    HIGH_MEMORY = "high_memory"
    HIGH_DISK = "high_disk"
    INTERFACE_DOWN = "interface_down"
    SECURITY_THREAT = "security_threat"
    NETWORK_ANOMALY = "network_anomaly"
    CUSTOM = "custom"


class Alert(BaseModel):
    """Alert model"""
    __tablename__ = "alerts"
    
    # Basic Information
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    alert_type = Column(Enum(AlertType), nullable=False)
    severity = Column(Enum(AlertSeverity), nullable=False)
    status = Column(Enum(AlertStatus), nullable=False, default=AlertStatus.OPEN)
    
    # Device Association
    device_id = Column(Integer, ForeignKey("devices.id"), nullable=True)
    device_ip = Column(String(45), nullable=True)  # For alerts without device record
    
    # Alert Details
    source = Column(String(100), nullable=True)  # Source system/module
    rule_id = Column(String(100), nullable=True)  # Alert rule identifier
    threshold_value = Column(String(100), nullable=True)  # Threshold that was exceeded
    current_value = Column(String(100), nullable=True)  # Current value
    
    # Timing
    first_occurrence = Column(String(50), nullable=True)  # ISO string
    last_occurrence = Column(String(50), nullable=True)  # ISO string
    resolved_at = Column(String(50), nullable=True)  # ISO string
    acknowledged_at = Column(String(50), nullable=True)  # ISO string
    
    # Assignment and Resolution
    assigned_to = Column(String(100), nullable=True)
    acknowledged_by = Column(String(100), nullable=True)
    resolved_by = Column(String(100), nullable=True)
    resolution_notes = Column(Text, nullable=True)
    
    # Additional Data
    metadata = Column(JSON, nullable=True)  # Additional alert data
    tags = Column(JSON, nullable=True)  # Alert tags
    
    # Relationships
    device = relationship("Device", back_populates="alerts")
    
    def __repr__(self):
        return f"<Alert(title='{self.title}', severity='{self.severity}', status='{self.status}')>"
    
    @property
    def is_active(self) -> bool:
        """Check if alert is active"""
        return self.status in [AlertStatus.OPEN, AlertStatus.ACKNOWLEDGED]
    
    @property
    def duration_minutes(self) -> Optional[int]:
        """Calculate alert duration in minutes"""
        if not self.first_occurrence:
            return None
        
        try:
            start = datetime.fromisoformat(self.first_occurrence.replace('Z', '+00:00'))
            end_time = self.resolved_at or self.last_occurrence
            if end_time:
                end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                return int((end - start).total_seconds() / 60)
            else:
                # Alert is still active
                now = datetime.now()
                return int((now - start).total_seconds() / 60)
        except (ValueError, TypeError):
            return None

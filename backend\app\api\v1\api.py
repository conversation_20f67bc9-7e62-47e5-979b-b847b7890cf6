"""
API v1 Router
"""
from fastapi import APIRouter

from .endpoints import devices, alerts, performance, ai, websocket, threat, monitoring

api_router = APIRouter()

# Include endpoint routers
api_router.include_router(devices.router, prefix="/devices", tags=["devices"])
api_router.include_router(alerts.router, prefix="/alerts", tags=["alerts"])
api_router.include_router(performance.router, prefix="/performance", tags=["performance"])
api_router.include_router(ai.router, prefix="/ai", tags=["ai"])
api_router.include_router(websocket.router, prefix="/ws", tags=["websocket"])
api_router.include_router(threat.router, prefix="/threat", tags=["threat"])
api_router.include_router(monitoring.router, prefix="/monitoring", tags=["monitoring"])

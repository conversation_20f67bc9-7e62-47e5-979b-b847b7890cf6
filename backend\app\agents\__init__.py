"""
AI Agents Package - 重构后的Agent系统
"""
from datetime import datetime
from .general_assistant import general_assistant
from .security_analyst import security_analyst
from .performance_analyst import performance_analyst

# Agent注册表
AGENTS = {
    'general': {
        'instance': general_assistant,
        'name': '通用助手',
        'description': '网络监控通用AI助手，提供设备状态、告警概览等基础信息'
    },
    'security': {
        'instance': security_analyst,
        'name': '安全专家',
        'description': '网络安全分析专家，专注威胁检测、安全事件分析和防护建议'
    },
    'performance': {
        'instance': performance_analyst,
        'name': '性能专家',
        'description': '网络性能分析专家，专注性能监控、瓶颈分析和优化建议'
    }
}

def get_agent(agent_type: str):
    """获取指定类型的Agent实例

    Args:
        agent_type: Agent类型 ('general', 'security', 'performance')

    Returns:
        Agent实例或None
    """
    agent_info = AGENTS.get(agent_type)
    return agent_info['instance'] if agent_info else None

def get_available_agents():
    """获取可用的Agent列表

    Returns:
        Agent信息列表
    """
    return [
        {
            'key': key,
            'name': info['name'],
            'description': info['description']
        }
        for key, info in AGENTS.items()
    ]

async def chat_with_agent(agent_type: str, message: str, context=None):
    """与指定Agent对话

    Args:
        agent_type: Agent类型
        message: 用户消息
        context: 上下文信息（可选）

    Returns:
        Agent响应结果
    """
    agent = get_agent(agent_type)
    if agent:
        return await agent.chat(message, context)
    else:
        return {
            'agent_name': 'Unknown',
            'agent_type': agent_type,
            'response': f'未找到类型为 {agent_type} 的Agent',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'error': f'Agent type {agent_type} not found',
            'available_agents': get_available_agents()
        }

async def test_agent_connections():
    """测试所有Agent连接

    Returns:
        测试结果字典
    """
    results = {}
    for agent_type in AGENTS.keys():
        try:
            result = await chat_with_agent(agent_type, "系统连接测试")
            results[agent_type] = {
                'status': 'success' if result.get('success') else 'error',
                'response': result.get('response', ''),
                'agent_name': result.get('agent_name', ''),
                'timestamp': result.get('timestamp', datetime.now().isoformat())
            }
        except Exception as e:
            results[agent_type] = {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    return results

__all__ = [
    'general_assistant',
    'security_analyst',
    'performance_analyst',
    'get_agent',
    'get_available_agents',
    'chat_with_agent',
    'test_agent_connections',
    'AGENTS'
]

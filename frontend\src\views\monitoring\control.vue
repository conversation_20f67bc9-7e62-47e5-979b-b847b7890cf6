<template>
  <div class="monitoring-control">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>监控服务控制台</h1>
      <a-space>
        <a-button @click="$router.back()">
          <template #icon><ArrowLeftOutlined /></template>
          返回
        </a-button>
        <a-button @click="refreshStatus" :loading="loading">
          <template #icon><ReloadOutlined /></template>
          刷新状态
        </a-button>
      </a-space>
    </div>

    <!-- 服务状态概览 -->
    <a-card title="服务状态" class="status-card">
      <template #extra>
        <a-space>
          <a-button
            v-if="!status.is_running"
            type="primary"
            @click="startMonitoring"
            :loading="operationLoading"
          >
            <template #icon><PlayCircleOutlined /></template>
            启动服务
          </a-button>
          <a-button
            v-else
            danger
            @click="stopMonitoring"
            :loading="operationLoading"
          >
            <template #icon><PauseCircleOutlined /></template>
            停止服务
          </a-button>
          <a-button @click="restartMonitoring" :loading="operationLoading">
            <template #icon><RedoOutlined /></template>
            重启服务
          </a-button>
        </a-space>
      </template>

      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic
            title="服务状态"
            :value="status.is_running ? '运行中' : '已停止'"
            :value-style="{ color: status.is_running ? '#52c41a' : '#ff4d4f' }"
          >
            <template #prefix>
              <component :is="status.is_running ? PlayCircleOutlined : PauseCircleOutlined" />
            </template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="运行时间"
            :value="formatUptime(status.uptime || 0)"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix><ClockCircleOutlined /></template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="监控设备"
            :value="status.monitored_devices"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix><DesktopOutlined /></template>
          </a-statistic>
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="活跃收集器"
            :value="status.active_collectors"
            :value-style="{ color: '#13c2c2' }"
          >
            <template #prefix><ApiOutlined /></template>
          </a-statistic>
        </a-col>
      </a-row>

      <!-- 性能指标 -->
      <a-divider>性能指标</a-divider>
      <a-row :gutter="16">
        <a-col :span="8">
          <div class="performance-item">
            <span class="performance-label">CPU使用率</span>
            <a-progress
              :percent="status.performance?.cpu_usage || 0"
              :stroke-color="getPerformanceColor(status.performance?.cpu_usage || 0)"
            />
          </div>
        </a-col>
        <a-col :span="8">
          <div class="performance-item">
            <span class="performance-label">内存使用率</span>
            <a-progress
              :percent="status.performance?.memory_usage || 0"
              :stroke-color="getPerformanceColor(status.performance?.memory_usage || 0)"
            />
          </div>
        </a-col>
        <a-col :span="8">
          <div class="performance-item">
            <span class="performance-label">采集速率</span>
            <a-progress
              :percent="Math.min((status.performance?.collection_rate || 0) / 10 * 100, 100)"
              :format="() => `${status.performance?.collection_rate || 0}/s`"
              stroke-color="#52c41a"
            />
          </div>
        </a-col>
      </a-row>

      <!-- 错误和警告 -->
      <div v-if="status.errors?.length || status.warnings?.length" class="alerts-section">
        <a-divider>系统消息</a-divider>
        <a-alert
          v-for="error in status.errors"
          :key="error"
          :message="error"
          type="error"
          show-icon
          closable
          style="margin-bottom: 8px;"
        />
        <a-alert
          v-for="warning in status.warnings"
          :key="warning"
          :message="warning"
          type="warning"
          show-icon
          closable
          style="margin-bottom: 8px;"
        />
      </div>
    </a-card>

    <!-- 收集器状态 -->
    <a-card title="收集器状态" class="collectors-card">
      <template #extra>
        <a-button @click="loadCollectorStatus" :loading="collectorsLoading">
          刷新收集器状态
        </a-button>
      </template>

      <a-table
        :columns="collectorColumns"
        :data-source="collectors"
        :loading="collectorsLoading"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusLabel(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'collection_rate'">
            {{ record.collection_rate.toFixed(2) }}/s
          </template>
          <template v-else-if="column.key === 'error_rate'">
            <a-progress
              :percent="Math.round(record.error_rate * 100)"
              size="small"
              :stroke-color="record.error_rate > 0.1 ? '#ff4d4f' : '#52c41a'"
            />
          </template>
          <template v-else-if="column.key === 'success_rate'">
            <a-progress
              :percent="Math.round(record.performance_metrics.success_rate * 100)"
              size="small"
              :stroke-color="record.performance_metrics.success_rate > 0.9 ? '#52c41a' : '#faad14'"
            />
          </template>
          <template v-else-if="column.key === 'last_collection'">
            {{ formatTime(record.last_collection) }}
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-button
              size="small"
              @click="restartCollector(record.collector_id)"
              :loading="restartingCollectors.includes(record.collector_id)"
            >
              重启
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 监控统计 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-card title="采集统计" :loading="statsLoading">
          <template #extra>
            <a-select v-model:value="statsTimeRange" @change="loadStatistics" style="width: 120px;">
              <a-select-option value="1h">最近1小时</a-select-option>
              <a-select-option value="24h">最近24小时</a-select-option>
              <a-select-option value="7d">最近7天</a-select-option>
              <a-select-option value="30d">最近30天</a-select-option>
            </a-select>
          </template>

          <a-descriptions :column="1" size="small">
            <a-descriptions-item label="总采集次数">
              {{ statistics.collection_stats?.total_collections || 0 }}
            </a-descriptions-item>
            <a-descriptions-item label="成功采集">
              {{ statistics.collection_stats?.successful_collections || 0 }}
            </a-descriptions-item>
            <a-descriptions-item label="失败采集">
              {{ statistics.collection_stats?.failed_collections || 0 }}
            </a-descriptions-item>
            <a-descriptions-item label="平均采集时间">
              {{ (statistics.collection_stats?.avg_collection_time || 0).toFixed(2) }}ms
            </a-descriptions-item>
            <a-descriptions-item label="成功率">
              <a-progress
                :percent="getSuccessRate()"
                size="small"
                :stroke-color="getSuccessRate() > 90 ? '#52c41a' : '#faad14'"
              />
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>

      <a-col :span="12">
        <a-card title="设备统计" :loading="statsLoading">
          <a-descriptions :column="1" size="small">
            <a-descriptions-item label="总设备数">
              {{ statistics.device_stats?.total_devices || 0 }}
            </a-descriptions-item>
            <a-descriptions-item label="在线设备">
              <span style="color: #52c41a;">{{ statistics.device_stats?.online_devices || 0 }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="离线设备">
              <span style="color: #ff4d4f;">{{ statistics.device_stats?.offline_devices || 0 }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="告警设备">
              <span style="color: #faad14;">{{ statistics.device_stats?.warning_devices || 0 }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="在线率">
              <a-progress
                :percent="getOnlineRate()"
                size="small"
                :stroke-color="getOnlineRate() > 90 ? '#52c41a' : '#faad14'"
              />
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>
    </a-row>

    <!-- 快速操作 -->
    <a-card title="快速操作" class="quick-actions">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small" hoverable @click="$router.push('/monitoring/discovery')">
            <template #cover>
              <div class="action-icon">
                <SearchOutlined style="font-size: 32px; color: #1890ff;" />
              </div>
            </template>
            <a-card-meta title="设备发现" description="扫描网络发现新设备" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small" hoverable @click="showConfigModal">
            <template #cover>
              <div class="action-icon">
                <SettingOutlined style="font-size: 32px; color: #52c41a;" />
              </div>
            </template>
            <a-card-meta title="监控配置" description="调整监控参数和阈值" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small" hoverable @click="showCleanupModal">
            <template #cover>
              <div class="action-icon">
                <DeleteOutlined style="font-size: 32px; color: #fa8c16;" />
              </div>
            </template>
            <a-card-meta title="数据清理" description="清理历史监控数据" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small" hoverable @click="showExportModal">
            <template #cover>
              <div class="action-icon">
                <DownloadOutlined style="font-size: 32px; color: '#722ed1';" />
              </div>
            </template>
            <a-card-meta title="数据导出" description="导出监控数据报告" />
          </a-card>
        </a-col>
      </a-row>
    </a-card>

    <!-- 配置模态框 -->
    <a-modal
      v-model:open="configModalVisible"
      title="监控配置"
      @ok="updateConfig"
      @cancel="loadConfig"
      :confirm-loading="configLoading"
      width="600px"
    >
      <a-form :model="config" layout="vertical">
        <a-form-item label="采集间隔 (秒)">
          <a-input-number v-model:value="config.collection_interval" :min="10" :max="3600" />
        </a-form-item>
        <a-form-item label="SNMP超时 (秒)">
          <a-input-number v-model:value="config.snmp_timeout" :min="1" :max="60" />
        </a-form-item>
        <a-form-item label="最大并发采集数">
          <a-input-number v-model:value="config.max_concurrent_collections" :min="1" :max="100" />
        </a-form-item>
        <a-form-item label="启用自动发现">
          <a-switch v-model:checked="config.enable_auto_discovery" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 清理模态框 -->
    <a-modal
      v-model:open="cleanupModalVisible"
      title="数据清理"
      @ok="cleanupData"
      :confirm-loading="cleanupLoading"
    >
      <a-form layout="vertical">
        <a-form-item label="清理选项">
          <a-checkbox-group v-model:value="cleanupOptions">
            <a-checkbox value="performance">性能数据</a-checkbox>
            <a-checkbox value="alerts">告警数据</a-checkbox>
            <a-checkbox value="logs">日志数据</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="保留天数">
          <a-input-number v-model:value="cleanupDays" :min="1" :max="365" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 导出模态框 -->
    <a-modal
      v-model:open="exportModalVisible"
      title="数据导出"
      @ok="exportData"
      :confirm-loading="exportLoading"
    >
      <a-form layout="vertical">
        <a-form-item label="数据类型">
          <a-select v-model:value="exportType">
            <a-select-option value="performance">性能数据</a-select-option>
            <a-select-option value="alerts">告警数据</a-select-option>
            <a-select-option value="devices">设备数据</a-select-option>
            <a-select-option value="all">全部数据</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="导出格式">
          <a-select v-model:value="exportFormat">
            <a-select-option value="json">JSON</a-select-option>
            <a-select-option value="csv">CSV</a-select-option>
            <a-select-option value="excel">Excel</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="时间范围">
          <a-range-picker v-model:value="exportDateRange" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  RedoOutlined,
  ClockCircleOutlined,
  DesktopOutlined,
  ApiOutlined,
  SearchOutlined,
  SettingOutlined,
  DeleteOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import { monitoringApi, monitoringUtils, type MonitoringStatus, type CollectorStatus, type MonitoringConfig } from '@/api/monitoring'

// 响应式数据
const loading = ref(false)
const operationLoading = ref(false)
const collectorsLoading = ref(false)
const statsLoading = ref(false)
const configLoading = ref(false)
const cleanupLoading = ref(false)
const exportLoading = ref(false)

const configModalVisible = ref(false)
const cleanupModalVisible = ref(false)
const exportModalVisible = ref(false)

const status = ref<MonitoringStatus>({
  is_running: false,
  monitored_devices: 0,
  active_collectors: 0,
  performance: {
    cpu_usage: 0,
    memory_usage: 0,
    collection_rate: 0
  },
  errors: [],
  warnings: []
})

const collectors = ref<CollectorStatus[]>([])
const restartingCollectors = ref<string[]>([])

const statistics = ref<any>({})
const statsTimeRange = ref('24h')

const config = reactive<MonitoringConfig>({
  collection_interval: 60,
  snmp_timeout: 5,
  snmp_retries: 3,
  max_concurrent_collections: 10,
  enable_auto_discovery: false,
  discovery_interval: 3600,
  alert_thresholds: {
    cpu_threshold: 80,
    memory_threshold: 80,
    disk_threshold: 90,
    network_threshold: 80
  },
  retention_policy: {
    performance_data_days: 30,
    alert_data_days: 90,
    log_data_days: 7
  }
})

const cleanupOptions = ref(['performance'])
const cleanupDays = ref(30)
const exportType = ref('performance')
const exportFormat = ref('json')
const exportDateRange = ref()

// 表格列定义
const collectorColumns = [
  { title: '收集器ID', dataIndex: 'collector_id', key: 'collector_id', width: 120 },
  { title: '类型', dataIndex: 'collector_type', key: 'collector_type', width: 80 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 80 },
  { title: '分配设备', dataIndex: 'assigned_devices', key: 'assigned_devices', width: 80 },
  { title: '采集速率', dataIndex: 'collection_rate', key: 'collection_rate', width: 100 },
  { title: '错误率', dataIndex: 'error_rate', key: 'error_rate', width: 100 },
  { title: '成功率', dataIndex: 'success_rate', key: 'success_rate', width: 100 },
  { title: '最后采集', dataIndex: 'last_collection', key: 'last_collection', width: 150 },
  { title: '操作', key: 'actions', width: 80 }
]

// 工具函数
const formatUptime = (seconds: number) => {
  return monitoringUtils.formatUptime(seconds)
}

const formatTime = (timestamp?: string) => {
  return monitoringUtils.formatTimestamp(timestamp)
}

const getPerformanceColor = (value: number) => {
  if (value > 80) return '#ff4d4f'
  if (value > 60) return '#faad14'
  return '#52c41a'
}

const getStatusColor = (status: string) => {
  return monitoringUtils.getStatusColor(status)
}

const getStatusLabel = (status: string) => {
  const labels = {
    active: '活跃',
    inactive: '非活跃',
    error: '错误'
  }
  return labels[status as keyof typeof labels] || status
}

const getSuccessRate = () => {
  const stats = statistics.value.collection_stats
  if (!stats || stats.total_collections === 0) return 0
  return Math.round((stats.successful_collections / stats.total_collections) * 100)
}

const getOnlineRate = () => {
  const stats = statistics.value.device_stats
  if (!stats || stats.total_devices === 0) return 0
  return Math.round((stats.online_devices / stats.total_devices) * 100)
}

// 数据加载函数
const loadStatus = async () => {
  try {
    const response = await monitoringApi.getStatus()
    status.value = response.data
  } catch (error) {
    console.error('加载监控状态失败:', error)
    message.error('加载监控状态失败')
  }
}

const loadCollectorStatus = async () => {
  try {
    collectorsLoading.value = true
    const response = await monitoringApi.getCollectorStatus()
    collectors.value = response.data.collectors
  } catch (error) {
    console.error('加载收集器状态失败:', error)
    message.error('加载收集器状态失败')
  } finally {
    collectorsLoading.value = false
  }
}

const loadStatistics = async () => {
  try {
    statsLoading.value = true
    const response = await monitoringApi.getStatistics(statsTimeRange.value)
    statistics.value = response.data
  } catch (error) {
    console.error('加载统计信息失败:', error)
    message.error('加载统计信息失败')
  } finally {
    statsLoading.value = false
  }
}

const loadConfig = async () => {
  try {
    const response = await monitoringApi.getConfig()
    Object.assign(config, response.data)
  } catch (error) {
    console.error('加载配置失败:', error)
    message.error('加载配置失败')
  }
}

// 操作函数
const startMonitoring = async () => {
  try {
    operationLoading.value = true
    await monitoringApi.startMonitoring()
    message.success('监控服务启动成功')
    await refreshStatus()
  } catch (error) {
    console.error('启动监控服务失败:', error)
    message.error('启动监控服务失败')
  } finally {
    operationLoading.value = false
  }
}

const stopMonitoring = async () => {
  try {
    operationLoading.value = true
    await monitoringApi.stopMonitoring()
    message.success('监控服务停止成功')
    await refreshStatus()
  } catch (error) {
    console.error('停止监控服务失败:', error)
    message.error('停止监控服务失败')
  } finally {
    operationLoading.value = false
  }
}

const restartMonitoring = async () => {
  try {
    operationLoading.value = true
    await monitoringApi.restartMonitoring()
    message.success('监控服务重启成功')
    await refreshStatus()
  } catch (error) {
    console.error('重启监控服务失败:', error)
    message.error('重启监控服务失败')
  } finally {
    operationLoading.value = false
  }
}

const restartCollector = async (collectorId: string) => {
  try {
    restartingCollectors.value.push(collectorId)
    await monitoringApi.restartCollector(collectorId)
    message.success(`收集器 ${collectorId} 重启成功`)
    await loadCollectorStatus()
  } catch (error) {
    console.error('重启收集器失败:', error)
    message.error('重启收集器失败')
  } finally {
    restartingCollectors.value = restartingCollectors.value.filter(id => id !== collectorId)
  }
}

const refreshStatus = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadStatus(),
      loadCollectorStatus(),
      loadStatistics()
    ])
  } finally {
    loading.value = false
  }
}

const showConfigModal = () => {
  loadConfig()
  configModalVisible.value = true
}

const updateConfig = async () => {
  try {
    configLoading.value = true
    await monitoringApi.updateConfig(config)
    message.success('配置更新成功')
    configModalVisible.value = false
  } catch (error) {
    console.error('更新配置失败:', error)
    message.error('更新配置失败')
  } finally {
    configLoading.value = false
  }
}

const showCleanupModal = () => {
  cleanupModalVisible.value = true
}

const cleanupData = async () => {
  try {
    cleanupLoading.value = true
    const options = {
      cleanup_performance: cleanupOptions.value.includes('performance'),
      cleanup_alerts: cleanupOptions.value.includes('alerts'),
      cleanup_logs: cleanupOptions.value.includes('logs'),
      days_to_keep: cleanupDays.value
    }
    const response = await monitoringApi.cleanupData(options)
    message.success(`数据清理完成，清理了${response.data.cleaned_records}条记录`)
    cleanupModalVisible.value = false
  } catch (error) {
    console.error('数据清理失败:', error)
    message.error('数据清理失败')
  } finally {
    cleanupLoading.value = false
  }
}

const showExportModal = () => {
  exportModalVisible.value = true
}

const exportData = async () => {
  try {
    exportLoading.value = true
    const options = {
      data_type: exportType.value as any,
      format: exportFormat.value as any,
      start_date: exportDateRange.value?.[0]?.format('YYYY-MM-DD'),
      end_date: exportDateRange.value?.[1]?.format('YYYY-MM-DD')
    }
    const response = await monitoringApi.exportData(options)
    
    // 下载文件
    window.open(response.data.download_url, '_blank')
    message.success('数据导出成功')
    exportModalVisible.value = false
  } catch (error) {
    console.error('数据导出失败:', error)
    message.error('数据导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 组件挂载
onMounted(() => {
  refreshStatus()
})
</script>

<style scoped>
.monitoring-control {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.status-card {
  margin-bottom: 24px;
}

.performance-item {
  margin-bottom: 16px;
}

.performance-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.alerts-section {
  margin-top: 16px;
}

.collectors-card {
  margin-bottom: 24px;
}

.quick-actions .action-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  background: #f5f5f5;
}

.quick-actions .ant-card-small .ant-card-body {
  padding: 12px;
}
</style>

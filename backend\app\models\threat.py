"""
Threat Intelligence Data Models
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, Float, Boolean, Enum as SQLEnum
from sqlalchemy.sql import func
from datetime import datetime
from enum import Enum

from .base import Base


class ThreatLevel(str, Enum):
    """威胁级别枚举"""
    SAFE = "safe"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ThreatSource(str, Enum):
    """威胁情报来源枚举"""
    BUILTIN = "builtin"
    THREAT_FEED = "threat_feed"
    MANUAL = "manual"
    HONEYPOT = "honeypot"
    ANALYSIS = "analysis"


class ThreatIntelligence(Base):
    """威胁情报表"""
    __tablename__ = "threat_intelligence"

    id = Column(Integer, primary_key=True, index=True)
    
    # IP信息
    ip_address = Column(String(45), nullable=False, index=True, comment="IP地址")
    domain = Column(String(255), nullable=True, comment="域名")
    
    # 威胁信息
    is_malicious = Column(Boolean, default=False, comment="是否恶意")
    threat_level = Column(SQLEnum(ThreatLevel), default=ThreatLevel.LOW, comment="威胁级别")
    categories = Column(Text, nullable=True, comment="威胁类别，逗号分隔")
    description = Column(Text, nullable=True, comment="威胁描述")
    
    # 情报来源
    source = Column(SQLEnum(ThreatSource), default=ThreatSource.MANUAL, comment="情报来源")
    sources = Column(Text, nullable=True, comment="多个来源，逗号分隔")
    confidence_score = Column(Float, default=0.5, comment="置信度分数 0-1")
    
    # 时间信息
    first_seen = Column(DateTime, nullable=True, comment="首次发现时间")
    last_seen = Column(DateTime, nullable=True, comment="最后发现时间")
    expires_at = Column(DateTime, nullable=True, comment="过期时间")
    
    # 统计信息
    hit_count = Column(Integer, default=0, comment="命中次数")
    false_positive_count = Column(Integer, default=0, comment="误报次数")
    
    # 元数据
    metadata = Column(Text, nullable=True, comment="额外元数据JSON")
    tags = Column(Text, nullable=True, comment="标签，逗号分隔")
    
    # 审计字段
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    created_by = Column(String(100), nullable=True, comment="创建者")
    updated_by = Column(String(100), nullable=True, comment="更新者")
    is_active = Column(Boolean, default=True, comment="是否激活")

    def __repr__(self):
        return f"<ThreatIntelligence(ip={self.ip_address}, level={self.threat_level}, malicious={self.is_malicious})>"


class IPBlacklist(Base):
    """IP黑名单表"""
    __tablename__ = "ip_blacklist"

    id = Column(Integer, primary_key=True, index=True)
    
    # IP信息
    ip_address = Column(String(45), nullable=False, unique=True, index=True, comment="IP地址")
    ip_range = Column(String(50), nullable=True, comment="IP范围 CIDR格式")
    
    # 黑名单信息
    reason = Column(String(500), nullable=False, comment="加入黑名单原因")
    severity = Column(SQLEnum(ThreatLevel), default=ThreatLevel.MEDIUM, comment="严重程度")
    category = Column(String(100), nullable=True, comment="威胁类别")
    
    # 来源信息
    source = Column(String(100), default="manual", comment="来源")
    reference_url = Column(String(500), nullable=True, comment="参考链接")
    
    # 时间信息
    blocked_at = Column(DateTime, default=datetime.utcnow, comment="封禁时间")
    expires_at = Column(DateTime, nullable=True, comment="过期时间")
    last_hit = Column(DateTime, nullable=True, comment="最后命中时间")
    
    # 统计信息
    hit_count = Column(Integer, default=0, comment="命中次数")
    
    # 审计字段
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    added_by = Column(String(100), nullable=True, comment="添加者")
    is_active = Column(Boolean, default=True, comment="是否激活")

    def __repr__(self):
        return f"<IPBlacklist(ip={self.ip_address}, reason={self.reason}, severity={self.severity})>"


class ThreatEvent(Base):
    """威胁事件表"""
    __tablename__ = "threat_events"

    id = Column(Integer, primary_key=True, index=True)
    
    # 事件信息
    event_type = Column(String(100), nullable=False, comment="事件类型")
    event_name = Column(String(200), nullable=False, comment="事件名称")
    description = Column(Text, nullable=True, comment="事件描述")
    
    # 威胁信息
    threat_level = Column(SQLEnum(ThreatLevel), default=ThreatLevel.LOW, comment="威胁级别")
    confidence = Column(Float, default=0.5, comment="置信度")
    
    # 网络信息
    source_ip = Column(String(45), nullable=True, comment="源IP")
    destination_ip = Column(String(45), nullable=True, comment="目标IP")
    source_port = Column(Integer, nullable=True, comment="源端口")
    destination_port = Column(Integer, nullable=True, comment="目标端口")
    protocol = Column(String(20), nullable=True, comment="协议")
    
    # 设备信息
    device_id = Column(Integer, nullable=True, comment="关联设备ID")
    device_ip = Column(String(45), nullable=True, comment="设备IP")
    
    # 检测信息
    detection_method = Column(String(100), nullable=True, comment="检测方法")
    rule_id = Column(String(100), nullable=True, comment="规则ID")
    signature = Column(Text, nullable=True, comment="特征签名")
    
    # 响应信息
    status = Column(String(50), default="open", comment="处理状态")
    response_action = Column(String(100), nullable=True, comment="响应动作")
    analyst_notes = Column(Text, nullable=True, comment="分析师备注")
    
    # 时间信息
    detected_at = Column(DateTime, default=datetime.utcnow, comment="检测时间")
    resolved_at = Column(DateTime, nullable=True, comment="解决时间")
    
    # 审计字段
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    is_active = Column(Boolean, default=True, comment="是否激活")

    def __repr__(self):
        return f"<ThreatEvent(type={self.event_type}, level={self.threat_level}, source={self.source_ip})>"


class IOC(Base):
    """威胁指标表 (Indicators of Compromise)"""
    __tablename__ = "iocs"

    id = Column(Integer, primary_key=True, index=True)
    
    # IOC信息
    ioc_type = Column(String(50), nullable=False, comment="IOC类型: ip, domain, hash, url等")
    ioc_value = Column(String(500), nullable=False, index=True, comment="IOC值")
    
    # 威胁信息
    threat_type = Column(String(100), nullable=True, comment="威胁类型")
    malware_family = Column(String(100), nullable=True, comment="恶意软件家族")
    campaign = Column(String(100), nullable=True, comment="攻击活动")
    
    # 情报信息
    source = Column(String(100), nullable=True, comment="情报来源")
    confidence = Column(Float, default=0.5, comment="置信度")
    tlp = Column(String(20), default="white", comment="TLP标记")
    
    # 时间信息
    first_seen = Column(DateTime, nullable=True, comment="首次发现")
    last_seen = Column(DateTime, nullable=True, comment="最后发现")
    expires_at = Column(DateTime, nullable=True, comment="过期时间")
    
    # 审计字段
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    is_active = Column(Boolean, default=True, comment="是否激活")

    def __repr__(self):
        return f"<IOC(type={self.ioc_type}, value={self.ioc_value}, threat={self.threat_type})>"

"""
Security Analyst Agent - 网络安全分析专家
"""
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from loguru import logger

from agents import Agent, Runner, function_tool, set_tracing_disabled
from agents.extensions.models.litellm_model import LitellmModel

from ..core.config import settings
from ..core.database import SessionLocal
from ..models.alert import Alert, AlertType, AlertSeverity, AlertStatus
from ..models.threat import ThreatIntelligence, IPBlacklist, ThreatEvent
from ..services.threat_intelligence import threat_intelligence_service

# 禁用追踪功能
set_tracing_disabled(True)


@function_tool
async def check_ip_reputation(ip_address: str) -> str:
    """检查IP地址信誉
    
    Args:
        ip_address: 要检查的IP地址
    
    Returns:
        IP信誉检查结果
    """
    try:
        result = await threat_intelligence_service.check_ip_reputation(ip_address)
        
        if result.get("is_malicious"):
            return f"⚠️ IP {ip_address} 被标记为恶意！威胁级别：{result.get('threat_level')}，原因：{result.get('reason')}，置信度：{result.get('confidence', 0):.2f}"
        else:
            return f"✅ IP {ip_address} 暂未发现威胁，状态：{result.get('threat_level', 'safe')}"
            
    except Exception as e:
        logger.error(f"检查IP信誉失败: {e}")
        return f"检查IP {ip_address} 信誉时发生错误：{str(e)}"


@function_tool
async def get_security_alerts(hours: int = 24, severity: Optional[str] = None) -> str:
    """获取安全相关告警
    
    Args:
        hours: 查询最近多少小时的告警（默认24小时）
        severity: 告警级别过滤（可选）
    
    Returns:
        安全告警信息
    """
    try:
        with SessionLocal() as db:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            # 查询安全相关告警类型
            security_alert_types = [
                AlertType.SECURITY_BREACH,
                AlertType.INTRUSION_DETECTED,
                AlertType.MALWARE_DETECTED,
                AlertType.SUSPICIOUS_ACTIVITY
            ]
            
            query = db.query(Alert).filter(
                Alert.is_active == True,
                Alert.created_at >= start_time,
                Alert.alert_type.in_(security_alert_types)
            )
            
            if severity:
                try:
                    severity_enum = AlertSeverity(severity.lower())
                    query = query.filter(Alert.severity == severity_enum)
                except ValueError:
                    return f"无效的告警级别：{severity}"
            
            alerts = query.order_by(Alert.created_at.desc()).all()
            
            if not alerts:
                return f"最近{hours}小时内没有安全相关告警"
            
            result = f"🔒 最近{hours}小时安全告警（共{len(alerts)}个）：\n"
            for alert in alerts[:10]:  # 只显示最新10个
                status_icon = "🔴" if alert.status == AlertStatus.OPEN else "🟡" if alert.status == AlertStatus.ACKNOWLEDGED else "🟢"
                result += f"{status_icon} [{alert.severity.value.upper()}] {alert.title} - {alert.device_ip or 'N/A'} ({alert.created_at.strftime('%m-%d %H:%M')})\n"
            
            if len(alerts) > 10:
                result += f"... 还有{len(alerts) - 10}个告警"
            
            return result
            
    except Exception as e:
        logger.error(f"获取安全告警失败: {e}")
        return f"获取安全告警失败：{str(e)}"


@function_tool
async def get_threat_statistics() -> str:
    """获取威胁统计信息
    
    Returns:
        威胁统计概览
    """
    try:
        stats = await threat_intelligence_service.get_threat_statistics()
        
        result = "🛡️ 威胁情报统计：\n"
        result += f"威胁情报总数：{stats.get('total_threats', 0)}\n"
        result += f"高危威胁：{stats.get('high_severity_threats', 0)}\n"
        result += f"黑名单条目：{stats.get('blacklist_entries', 0)}\n"
        result += f"内置黑名单：{stats.get('builtin_blacklist_count', 0)}\n"
        
        if stats.get('last_update'):
            result += f"最后更新：{stats['last_update']}"
        
        return result
        
    except Exception as e:
        logger.error(f"获取威胁统计失败: {e}")
        return f"获取威胁统计失败：{str(e)}"


@function_tool
async def analyze_security_incident(incident_description: str) -> str:
    """分析安全事件
    
    Args:
        incident_description: 安全事件描述
    
    Returns:
        安全事件分析结果
    """
    try:
        # 这里可以集成更复杂的安全分析逻辑
        # 目前提供基础的关键词分析
        
        high_risk_keywords = ["攻击", "入侵", "恶意", "病毒", "木马", "勒索", "暴力破解", "SQL注入", "XSS"]
        medium_risk_keywords = ["异常", "可疑", "未授权", "失败", "错误", "超时"]
        
        description_lower = incident_description.lower()
        
        high_risk_found = [kw for kw in high_risk_keywords if kw in description_lower]
        medium_risk_found = [kw for kw in medium_risk_keywords if kw in description_lower]
        
        if high_risk_found:
            risk_level = "高危"
            risk_icon = "🚨"
            recommendations = [
                "立即隔离受影响的设备",
                "检查相关日志和网络流量",
                "更新安全策略和防护规则",
                "通知安全团队进行深入调查"
            ]
        elif medium_risk_found:
            risk_level = "中危"
            risk_icon = "⚠️"
            recommendations = [
                "监控相关设备状态",
                "检查是否有类似事件",
                "考虑加强监控策略",
                "记录事件用于后续分析"
            ]
        else:
            risk_level = "低危"
            risk_icon = "ℹ️"
            recommendations = [
                "记录事件信息",
                "定期回顾类似事件",
                "保持正常监控"
            ]
        
        result = f"{risk_icon} 安全事件分析结果：\n"
        result += f"风险级别：{risk_level}\n"
        result += f"检测到的关键词：{', '.join(high_risk_found + medium_risk_found) or '无特殊关键词'}\n"
        result += f"建议措施：\n"
        for i, rec in enumerate(recommendations, 1):
            result += f"{i}. {rec}\n"
        
        return result
        
    except Exception as e:
        logger.error(f"分析安全事件失败: {e}")
        return f"分析安全事件失败：{str(e)}"


@function_tool
async def get_blacklist_info() -> str:
    """获取黑名单信息
    
    Returns:
        黑名单概览信息
    """
    try:
        blacklist = await threat_intelligence_service.get_blacklist()
        
        if not blacklist:
            return "当前黑名单为空"
        
        result = f"🚫 IP黑名单（共{len(blacklist)}个）：\n"
        
        # 按严重程度分组
        critical_ips = [item for item in blacklist if item.get('severity') == 'critical']
        high_ips = [item for item in blacklist if item.get('severity') == 'high']
        medium_ips = [item for item in blacklist if item.get('severity') == 'medium']
        
        if critical_ips:
            result += f"🔴 严重威胁（{len(critical_ips)}个）：\n"
            for item in critical_ips[:5]:
                result += f"  - {item['ip_address']}: {item['reason']}\n"
        
        if high_ips:
            result += f"🟠 高危威胁（{len(high_ips)}个）：\n"
            for item in high_ips[:5]:
                result += f"  - {item['ip_address']}: {item['reason']}\n"
        
        if medium_ips:
            result += f"🟡 中等威胁（{len(medium_ips)}个）：\n"
            for item in medium_ips[:3]:
                result += f"  - {item['ip_address']}: {item['reason']}\n"
        
        if len(blacklist) > 13:
            result += f"... 还有{len(blacklist) - 13}个条目"
        
        return result
        
    except Exception as e:
        logger.error(f"获取黑名单信息失败: {e}")
        return f"获取黑名单信息失败：{str(e)}"


class SecurityAnalystAgent:
    """网络安全分析专家Agent"""
    
    def __init__(self):
        """初始化安全分析Agent"""
        try:
            # 创建LiteLLM模型实例
            self.model = LitellmModel(
                model=f"deepseek/{settings.DEEPSEEK_MODEL}",
                api_key=settings.DEEPSEEK_API_KEY,
                base_url=settings.DEEPSEEK_BASE_URL
            )
            
            # 创建Agent实例
            self.agent = Agent(
                name="CampusGuard安全专家",
                instructions="""你是CampusGuard网络监控系统的安全分析专家。你的专业领域包括：

1. 网络安全威胁检测和分析
2. 安全告警的评估和处理建议
3. 网络入侵检测和响应
4. 恶意IP和威胁情报分析
5. 安全事件调查和取证
6. 安全策略和防护建议

你具备以下专业知识：
- 网络攻击模式识别
- 恶意软件分析
- 入侵检测系统(IDS/IPS)
- 威胁情报分析
- 安全事件响应流程
- 网络取证技术

请用中文回复，使用专业的安全术语，提供详细的分析和明确的建议。对于高危威胁，请特别强调紧急性和具体的应对措施。""",
                model=self.model,
                tools=[
                    check_ip_reputation,
                    get_security_alerts,
                    get_threat_statistics,
                    analyze_security_incident,
                    get_blacklist_info
                ]
            )
            
            logger.info("安全分析Agent初始化成功")
            
        except Exception as e:
            logger.error(f"安全分析Agent初始化失败: {e}")
            raise
    
    async def chat(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """与安全专家对话
        
        Args:
            message: 用户消息
            context: 上下文信息（可选）
        
        Returns:
            包含响应信息的字典
        """
        try:
            logger.info(f"安全专家收到消息: {message}")
            
            # 运行Agent
            result = await Runner.run(
                self.agent,
                input=message,
                max_turns=5
            )
            
            response_data = {
                "agent_name": "安全专家",
                "agent_type": "security_analyst",
                "response": result.final_output,
                "timestamp": datetime.now().isoformat(),
                "success": True
            }
            
            logger.info("安全专家响应成功")
            return response_data
            
        except Exception as e:
            logger.error(f"安全专家对话失败: {e}")
            return {
                "agent_name": "安全专家",
                "agent_type": "security_analyst",
                "response": f"抱歉，处理您的安全咨询时发生错误：{str(e)}",
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": str(e)
            }


# 全局安全专家实例
security_analyst = SecurityAnalystAgent()

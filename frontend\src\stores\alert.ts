import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { alertApi } from '@/api/alert'
import type { Alert, AlertStats } from '@/types/alert'

export const useAlertStore = defineStore('alert', () => {
  // State
  const alerts = ref<Alert[]>([])
  const stats = ref<AlertStats | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const openAlerts = computed(() => 
    alerts.value.filter(alert => alert.status === 'open')
  )

  const acknowledgedAlerts = computed(() => 
    alerts.value.filter(alert => alert.status === 'acknowledged')
  )

  const resolvedAlerts = computed(() => 
    alerts.value.filter(alert => alert.status === 'resolved')
  )

  const criticalAlerts = computed(() => 
    alerts.value.filter(alert => alert.severity === 'critical')
  )

  const highAlerts = computed(() => 
    alerts.value.filter(alert => alert.severity === 'high')
  )

  const alertsBySeverity = computed(() => {
    const severities: Record<string, Alert[]> = {}
    alerts.value.forEach(alert => {
      if (!severities[alert.severity]) {
        severities[alert.severity] = []
      }
      severities[alert.severity].push(alert)
    })
    return severities
  })

  const alertsByType = computed(() => {
    const types: Record<string, Alert[]> = {}
    alerts.value.forEach(alert => {
      if (!types[alert.alert_type]) {
        types[alert.alert_type] = []
      }
      types[alert.alert_type].push(alert)
    })
    return types
  })

  // Actions
  const fetchAlerts = async (params?: any) => {
    try {
      loading.value = true
      error.value = null
      const response = await alertApi.getAlerts(params)
      alerts.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取告警列表失败'
      console.error('Failed to fetch alerts:', err)
    } finally {
      loading.value = false
    }
  }

  const createAlert = async (alertData: Partial<Alert>) => {
    try {
      loading.value = true
      error.value = null
      const response = await alertApi.createAlert(alertData)
      alerts.value.unshift(response.data)
      return response.data
    } catch (err: any) {
      error.value = err.message || '创建告警失败'
      console.error('Failed to create alert:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const acknowledgeAlert = async (id: number) => {
    try {
      loading.value = true
      error.value = null
      await alertApi.acknowledgeAlert(id)
      const alert = alerts.value.find(a => a.id === id)
      if (alert) {
        alert.status = 'acknowledged'
        alert.acknowledged_at = new Date().toISOString()
      }
    } catch (err: any) {
      error.value = err.message || '确认告警失败'
      console.error('Failed to acknowledge alert:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const resolveAlert = async (id: number, resolutionNotes?: string) => {
    try {
      loading.value = true
      error.value = null
      await alertApi.resolveAlert(id, resolutionNotes)
      const alert = alerts.value.find(a => a.id === id)
      if (alert) {
        alert.status = 'resolved'
        alert.resolved_at = new Date().toISOString()
        alert.resolution_notes = resolutionNotes
      }
    } catch (err: any) {
      error.value = err.message || '解决告警失败'
      console.error('Failed to resolve alert:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchStats = async () => {
    try {
      const response = await alertApi.getStats()
      stats.value = response.data
    } catch (err: any) {
      console.error('Failed to fetch alert stats:', err)
    }
  }

  const fetchStatistics = async (hours = 24) => {
    try {
      const response = await alertApi.getStatistics(hours)
      return response.data
    } catch (err: any) {
      console.error('Failed to fetch alert statistics:', err)
      throw err
    }
  }

  const escalateAlerts = async () => {
    try {
      const response = await alertApi.escalateAlerts()
      return response.data
    } catch (err: any) {
      console.error('Failed to escalate alerts:', err)
      throw err
    }
  }

  const cleanupOldAlerts = async (days = 30) => {
    try {
      const response = await alertApi.cleanupOldAlerts(days)
      return response.data
    } catch (err: any) {
      console.error('Failed to cleanup old alerts:', err)
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  // Real-time updates
  const addAlert = (alert: Alert) => {
    alerts.value.unshift(alert)
  }

  const updateAlert = (updatedAlert: Alert) => {
    const index = alerts.value.findIndex(alert => alert.id === updatedAlert.id)
    if (index !== -1) {
      alerts.value[index] = updatedAlert
    }
  }

  const removeAlert = (id: number) => {
    alerts.value = alerts.value.filter(alert => alert.id !== id)
  }

  return {
    // State
    alerts,
    stats,
    loading,
    error,

    // Getters
    openAlerts,
    acknowledgedAlerts,
    resolvedAlerts,
    criticalAlerts,
    highAlerts,
    alertsBySeverity,
    alertsByType,

    // Actions
    fetchAlerts,
    createAlert,
    acknowledgeAlert,
    resolveAlert,
    fetchStats,
    fetchStatistics,
    escalateAlerts,
    cleanupOldAlerts,
    clearError,

    // Real-time updates
    addAlert,
    updateAlert,
    removeAlert
  }
})

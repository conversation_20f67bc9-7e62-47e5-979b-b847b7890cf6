"""
Threat Intelligence API endpoints
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from datetime import datetime

from ....core.database import get_db
from ....services.threat_intelligence import threat_intelligence_service
from ....schemas.threat import (
    ThreatIntelligenceResponse,
    IPReputationRequest,
    IPReputationResponse,
    BlacklistRequest,
    BlacklistResponse,
    ThreatStatisticsResponse
)

router = APIRouter()


@router.post("/check-ip", response_model=IPReputationResponse)
async def check_ip_reputation(
    request: IPReputationRequest,
    db: Session = Depends(get_db)
):
    """检查IP地址信誉"""
    try:
        result = await threat_intelligence_service.check_ip_reputation(request.ip_address)
        return IPReputationResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检查IP信誉失败: {str(e)}")


@router.post("/batch-check-ips")
async def batch_check_ips(
    ip_addresses: List[str],
    db: Session = Depends(get_db)
):
    """批量检查IP地址信誉"""
    try:
        results = await threat_intelligence_service.batch_check_ips(ip_addresses)
        return {
            "results": results,
            "total_checked": len(ip_addresses),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量检查IP失败: {str(e)}")


@router.get("/blacklist", response_model=List[BlacklistResponse])
async def get_blacklist(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """获取IP黑名单"""
    try:
        blacklist = await threat_intelligence_service.get_blacklist()
        
        # 分页处理
        total = len(blacklist)
        paginated_list = blacklist[skip:skip + limit]
        
        return {
            "items": [BlacklistResponse(**item) for item in paginated_list],
            "total": total,
            "skip": skip,
            "limit": limit
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取黑名单失败: {str(e)}")


@router.post("/blacklist/add")
async def add_to_blacklist(
    request: BlacklistRequest,
    db: Session = Depends(get_db)
):
    """添加IP到黑名单"""
    try:
        success = await threat_intelligence_service.add_to_blacklist(
            ip_address=request.ip_address,
            reason=request.reason,
            severity=request.severity
        )
        
        if success:
            return {
                "success": True,
                "message": f"IP {request.ip_address} 已添加到黑名单",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail="添加到黑名单失败")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"添加黑名单失败: {str(e)}")


@router.delete("/blacklist/{ip_address}")
async def remove_from_blacklist(
    ip_address: str,
    db: Session = Depends(get_db)
):
    """从黑名单移除IP"""
    try:
        success = await threat_intelligence_service.remove_from_blacklist(ip_address)
        
        if success:
            return {
                "success": True,
                "message": f"IP {ip_address} 已从黑名单移除",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=404, detail="IP不在黑名单中")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"移除黑名单失败: {str(e)}")


@router.post("/update")
async def update_threat_intelligence(
    db: Session = Depends(get_db)
):
    """更新威胁情报数据"""
    try:
        success = await threat_intelligence_service.update_threat_intelligence()
        
        if success:
            return {
                "success": True,
                "message": "威胁情报数据更新成功",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail="威胁情报更新失败")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新威胁情报失败: {str(e)}")


@router.get("/statistics", response_model=ThreatStatisticsResponse)
async def get_threat_statistics(
    db: Session = Depends(get_db)
):
    """获取威胁统计信息"""
    try:
        stats = await threat_intelligence_service.get_threat_statistics()
        return ThreatStatisticsResponse(**stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取威胁统计失败: {str(e)}")


@router.get("/intelligence", response_model=List[ThreatIntelligenceResponse])
async def get_threat_intelligence(
    ip_address: Optional[str] = Query(None),
    threat_level: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """获取威胁情报数据"""
    try:
        from ....models.threat import ThreatIntelligence
        
        query = db.query(ThreatIntelligence).filter(ThreatIntelligence.is_active == True)
        
        if ip_address:
            query = query.filter(ThreatIntelligence.ip_address == ip_address)
        
        if threat_level:
            query = query.filter(ThreatIntelligence.threat_level == threat_level)
        
        total = query.count()
        intelligence_data = query.offset(skip).limit(limit).all()
        
        return {
            "items": [
                ThreatIntelligenceResponse(
                    id=item.id,
                    ip_address=item.ip_address,
                    domain=item.domain,
                    is_malicious=item.is_malicious,
                    threat_level=item.threat_level,
                    categories=item.categories.split(",") if item.categories else [],
                    description=item.description,
                    source=item.source,
                    confidence_score=item.confidence_score,
                    first_seen=item.first_seen,
                    last_seen=item.last_seen,
                    created_at=item.created_at
                ) for item in intelligence_data
            ],
            "total": total,
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取威胁情报失败: {str(e)}")

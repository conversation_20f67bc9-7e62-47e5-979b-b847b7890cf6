<template>
  <div class="ai-analysis">
    <a-page-header
      title="AI智能分析"
      sub-title="基于DeepSeek V3的网络智能分析系统"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button @click="refreshAnalysisHistory" :loading="loadingHistory">
            <template #icon><ReloadOutlined /></template>
            刷新历史
          </a-button>
          <a-button @click="exportAnalysisResults">
            <template #icon><DownloadOutlined /></template>
            导出结果
          </a-button>
          <a-button type="primary" @click="showAnalysisHistory = true">
            <template #icon><HistoryOutlined /></template>
            分析历史
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <a-row :gutter="16" class="analysis-container">
      <!-- Analysis Configuration Sidebar -->
      <a-col :xs="24" :lg="6" class="config-sidebar">
        <a-card title="分析配置" size="small" class="config-card">
          <!-- Analysis Type Selection -->
          <div class="analysis-type-section">
            <h4>分析类型</h4>
            <a-select
              v-model:value="selectedAnalysisType"
              style="width: 100%"
              size="large"
              @change="handleAnalysisTypeChange"
            >
              <a-select-option :value="AnalysisType.NETWORK_ANALYSIS">
                <div class="analysis-option">
                  <NetworkOutlined style="margin-right: 8px; color: #1890ff;" />
                  网络分析
                </div>
              </a-select-option>
              <a-select-option :value="AnalysisType.SECURITY_ANALYSIS">
                <div class="analysis-option">
                  <SecurityScanOutlined style="margin-right: 8px; color: #ff4d4f;" />
                  安全分析
                </div>
              </a-select-option>
              <a-select-option :value="AnalysisType.PERFORMANCE_ANALYSIS">
                <div class="analysis-option">
                  <LineChartOutlined style="margin-right: 8px; color: #52c41a;" />
                  性能分析
                </div>
              </a-select-option>
              <a-select-option :value="AnalysisType.ANOMALY_DETECTION">
                <div class="analysis-option">
                  <ExclamationCircleOutlined style="margin-right: 8px; color: #fa8c16;" />
                  异常检测
                </div>
              </a-select-option>
            </a-select>
          </div>

          <a-divider />

          <!-- Agent Selection -->
          <div class="agent-selection">
            <h4>AI专家</h4>
            <a-select
              v-model:value="selectedAgent"
              style="width: 100%"
              size="large"
              @change="handleAgentChange"
            >
              <a-select-option
                v-for="agent in availableAgents"
                :key="agent.key"
                :value="agent.key"
              >
                <div class="agent-option">
                  <a-avatar :src="agent.avatar" size="small" style="margin-right: 8px;" />
                  <div class="agent-info">
                    <div class="agent-name">{{ agent.name }}</div>
                    <div class="agent-desc">{{ agent.description }}</div>
                  </div>
                </div>
              </a-select-option>
            </a-select>
          </div>

          <a-divider />

          <!-- Quick Analysis Templates -->
          <div class="quick-templates">
            <h4>快速模板</h4>
            <div class="template-buttons">
              <a-button
                v-for="template in analysisTemplates"
                :key="template.key"
                type="text"
                size="small"
                @click="handleTemplateClick(template)"
                class="template-button"
              >
                <component :is="template.icon" />
                {{ template.label }}
              </a-button>
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- AI Analysis Interface -->
      <a-col :xs="24" :lg="18" class="analysis-main">
        <a-card class="analysis-card">
          <!-- Analysis Header -->
          <template #title>
            <div class="analysis-header">
              <component :is="getAnalysisIcon()" style="margin-right: 8px;" />
              <span>{{ getAnalysisTitle() }}</span>
              <a-tag :color="getAnalysisTypeColor()" style="margin-left: 12px;">
                {{ getCurrentAgent().name }}
              </a-tag>
            </div>
          </template>

          <template #extra>
            <a-space>
              <a-button
                type="text"
                :icon="h(ClearOutlined)"
                @click="clearAnalysisResults"
                title="清空结果"
              />
              <a-button
                type="text"
                :icon="h(SettingOutlined)"
                @click="showAnalysisSettings = true"
                title="分析设置"
              />
            </a-space>
          </template>

          <!-- AI Analysis Interface using ant-design-x-vue -->
          <div class="ai-analysis-container">
            <!-- Analysis Results Display -->
            <div class="analysis-results-container" ref="resultsContainer">
              <!-- Analysis Results using AXBubble -->
              <AXBubble
                v-for="result in analysisResults"
                :key="result.id"
                :content="formatAnalysisResult(result)"
                type="ai"
                :avatar="getCurrentAgent().avatar"
                :status="result.status"
                :actions="analysisResultActions"
                class="analysis-result-bubble"
              >
                <template #content>
                  <div class="analysis-result-content">
                    <!-- Analysis Header -->
                    <div class="result-header">
                      <h4>{{ result.title }}</h4>
                      <div class="result-meta">
                        <a-tag :color="getAnalysisTypeColor()">
                          {{ getAnalysisTypeName(result.analysis_type) }}
                        </a-tag>
                        <span class="result-time">
                          {{ formatTimestamp(result.analyzed_at) }}
                        </span>
                        <a-progress
                          v-if="result.confidence_score"
                          :percent="Math.round(result.confidence_score * 100)"
                          size="small"
                          :stroke-color="getConfidenceColor(result.confidence_score)"
                          style="width: 100px; margin-left: 8px;"
                        />
                      </div>
                    </div>

                    <!-- Analysis Summary -->
                    <div v-if="result.summary" class="result-summary">
                      <h5>分析摘要</h5>
                      <p>{{ result.summary }}</p>
                    </div>

                    <!-- Analysis Findings -->
                    <div v-if="result.findings" class="result-findings">
                      <h5>分析发现</h5>
                      <div class="findings-content">
                        <pre v-if="typeof result.findings === 'string'">{{ result.findings }}</pre>
                        <div v-else>
                          <div v-for="(value, key) in result.findings" :key="key" class="finding-item">
                            <strong>{{ key }}:</strong> {{ formatFindingValue(value) }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Analysis Recommendations -->
                    <div v-if="result.recommendations" class="result-recommendations">
                      <h5>建议措施</h5>
                      <div class="recommendations-content">
                        <div v-for="(value, key) in result.recommendations" :key="key" class="recommendation-item">
                          <a-tag color="blue">{{ key }}</a-tag>
                          <span>{{ formatRecommendationValue(value) }}</span>
                        </div>
                      </div>
                    </div>

                    <!-- Processing Information -->
                    <div class="result-processing-info">
                      <a-descriptions size="small" :column="3">
                        <a-descriptions-item label="处理时间">
                          {{ formatProcessingTime(result.processing_time) }}
                        </a-descriptions-item>
                        <a-descriptions-item label="使用模型">
                          {{ result.model_used || 'DeepSeek V3' }}
                        </a-descriptions-item>
                        <a-descriptions-item label="Token使用">
                          {{ formatTokenUsage(result.token_usage) }}
                        </a-descriptions-item>
                      </a-descriptions>
                    </div>
                  </div>
                </template>
              </AXBubble>

              <!-- Empty State -->
              <div v-if="analysisResults.length === 0" class="empty-state">
                <a-empty description="开始AI智能分析吧！">
                  <template #image>
                    <RobotOutlined style="font-size: 64px; color: #d9d9d9;" />
                  </template>
                  <template #description>
                    <span>选择分析类型，输入分析查询，让AI为您提供专业的网络分析</span>
                  </template>
                </a-empty>
              </div>
            </div>

            <!-- Analysis Input using AXPrompt -->
            <AXPrompt
              v-model="analysisQuery"
              :loading="analyzing"
              :placeholder="`请输入${getAnalysisTitle()}查询...`"
              :auto-size="{ minRows: 2, maxRows: 6 }"
              @submit="handleAnalysisSubmit"
              @clear="handleAnalysisClear"
              class="analysis-input-prompt"
            >
              <template #actions>
                <a-space>
                  <a-upload
                    :before-upload="handleFileUpload"
                    :show-upload-list="false"
                    accept=".txt,.json,.csv,.log"
                  >
                    <a-button type="text" size="small">
                      <template #icon><PaperClipOutlined /></template>
                      附加文件
                    </a-button>
                  </a-upload>
                  <a-button
                    type="text"
                    size="small"
                    @click="showQuickTemplates = !showQuickTemplates"
                  >
                    <template #icon><ThunderboltOutlined /></template>
                    快速模板
                  </a-button>
                </a-space>
              </template>
            </AXPrompt>

            <!-- Quick Templates Overlay -->
            <div v-if="showQuickTemplates" class="quick-templates-overlay">
              <div class="templates-content">
                <h4>快速分析模板</h4>
                <div class="templates-grid">
                  <a-button
                    v-for="template in analysisTemplates"
                    :key="template.key"
                    type="text"
                    @click="handleTemplateClick(template)"
                    class="template-card"
                  >
                    <div class="template-content">
                      <component :is="template.icon" class="template-icon" />
                      <div class="template-text">
                        <div class="template-title">{{ template.label }}</div>
                        <div class="template-desc">{{ template.description }}</div>
                      </div>
                    </div>
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- Analysis History Modal -->
    <a-modal
      v-model:open="showAnalysisHistory"
      title="分析历史记录"
      width="1200px"
      :footer="null"
    >
      <div class="analysis-history-content">
        <!-- History Filters -->
        <a-form layout="inline" :model="historyFilters" class="history-filters">
          <a-form-item label="分析类型">
            <a-select
              v-model:value="historyFilters.analysis_type"
              placeholder="选择类型"
              style="width: 150px"
              allowClear
              @change="loadAnalysisHistory"
            >
              <a-select-option :value="AnalysisType.NETWORK_ANALYSIS">网络分析</a-select-option>
              <a-select-option :value="AnalysisType.SECURITY_ANALYSIS">安全分析</a-select-option>
              <a-select-option :value="AnalysisType.PERFORMANCE_ANALYSIS">性能分析</a-select-option>
              <a-select-option :value="AnalysisType.ANOMALY_DETECTION">异常检测</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="时间范围">
            <a-range-picker
              v-model:value="historyFilters.dateRange"
              format="YYYY-MM-DD"
              @change="loadAnalysisHistory"
            />
          </a-form-item>

          <a-form-item>
            <a-button type="primary" @click="loadAnalysisHistory" :loading="loadingHistory">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
          </a-form-item>
        </a-form>

        <!-- History Table -->
        <a-table
          :columns="historyColumns"
          :data-source="analysisHistory"
          :loading="loadingHistory"
          :pagination="historyPagination"
          @change="handleHistoryTableChange"
          size="small"
          :scroll="{ y: 400 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'analysis_type'">
              <a-tag :color="getAnalysisTypeColor(record.analysis_type)">
                {{ getAnalysisTypeName(record.analysis_type) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'title'">
              <a-button type="link" @click="viewHistoryDetail(record)">
                {{ record.title }}
              </a-button>
            </template>
            <template v-else-if="column.key === 'confidence_score'">
              <a-progress
                v-if="record.confidence_score"
                :percent="Math.round(record.confidence_score * 100)"
                size="small"
                :stroke-color="getConfidenceColor(record.confidence_score)"
              />
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'analyzed_at'">
              {{ formatTimestamp(record.analyzed_at) }}
            </template>
            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="viewHistoryDetail(record)">
                  查看
                </a-button>
                <a-button type="link" size="small" @click="rerunAnalysis(record)">
                  重新分析
                </a-button>
                <a-button type="link" size="small" @click="exportSingleResult(record)">
                  导出
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- Analysis Settings Modal -->
    <a-modal
      v-model:open="showAnalysisSettings"
      title="分析设置"
      width="600px"
      @ok="saveAnalysisSettings"
    >
      <a-form :model="analysisSettings" layout="vertical">
        <a-form-item label="分析深度">
          <a-slider
            v-model:value="analysisSettings.depth"
            :min="1"
            :max="5"
            :marks="{ 1: '简单', 3: '标准', 5: '深度' }"
          />
        </a-form-item>

        <a-form-item label="置信度阈值">
          <a-slider
            v-model:value="analysisSettings.confidenceThreshold"
            :min="0"
            :max="1"
            :step="0.1"
            :marks="{ 0: '0%', 0.5: '50%', 1: '100%' }"
          />
        </a-form-item>

        <a-form-item label="自动保存结果">
          <a-switch v-model:checked="analysisSettings.autoSave" />
        </a-form-item>

        <a-form-item label="实时分析">
          <a-switch v-model:checked="analysisSettings.realTimeAnalysis" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, h, computed } from 'vue'
import { message } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  Bubble as AXBubble,
  Prompt as AXPrompt
} from 'ant-design-x-vue'
import {
  aiApi,
  AnalysisType,
  type AnalysisRequest,
  type AnalysisResponse,
  type AgentType,
  type AnalysisResultsParams
} from '@/api/ai'
import {
  NetworkOutlined,
  SecurityScanOutlined,
  LineChartOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  DownloadOutlined,
  HistoryOutlined,
  ClearOutlined,
  SettingOutlined,
  RobotOutlined,
  PaperClipOutlined,
  ThunderboltOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'

// 分析模板接口
interface AnalysisTemplate {
  key: string
  label: string
  description: string
  icon: any
  query: string
}

// 分析设置接口
interface AnalysisSettings {
  depth: number
  confidenceThreshold: number
  autoSave: boolean
  realTimeAnalysis: boolean
}

// 历史筛选接口
interface HistoryFilters {
  analysis_type?: AnalysisType
  dateRange?: [Dayjs, Dayjs]
}

// 响应式数据
const analyzing = ref(false)
const loadingHistory = ref(false)
const showAnalysisHistory = ref(false)
const showAnalysisSettings = ref(false)
const showQuickTemplates = ref(false)

// 分析配置
const selectedAnalysisType = ref<AnalysisType>(AnalysisType.NETWORK_ANALYSIS)
const selectedAgent = ref<string>('general')
const analysisQuery = ref('')
const analysisResults = ref<AnalysisResponse[]>([])
const analysisHistory = ref<AnalysisResponse[]>([])

// 容器引用
const resultsContainer = ref<HTMLElement>()

// 分析设置
const analysisSettings = ref<AnalysisSettings>({
  depth: 3,
  confidenceThreshold: 0.7,
  autoSave: true,
  realTimeAnalysis: false
})

// 历史筛选
const historyFilters = ref<HistoryFilters>({})

// 分页配置
const historyPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
})

// 可用的Agent列表
const availableAgents = ref<AgentType[]>([
  {
    key: 'general',
    name: '通用助手',
    description: '网络监控通用AI助手，提供设备状态、告警概览等基础信息',
    avatar: '/avatars/general.png'
  },
  {
    key: 'security',
    name: '安全专家',
    description: '网络安全分析专家，专注威胁检测、安全事件分析和防护建议',
    avatar: '/avatars/security.png'
  },
  {
    key: 'performance',
    name: '性能专家',
    description: '网络性能分析专家，专注性能监控、瓶颈分析和优化建议',
    avatar: '/avatars/performance.png'
  }
])

// 分析模板
const analysisTemplates = ref<AnalysisTemplate[]>([
  {
    key: 'network-topology',
    label: '网络拓扑分析',
    description: '分析网络拓扑结构和连接状态',
    icon: NetworkOutlined,
    query: '请分析当前网络拓扑结构，包括设备连接状态、网络层次和潜在的单点故障'
  },
  {
    key: 'security-scan',
    label: '安全威胁扫描',
    description: '检测网络中的安全威胁和漏洞',
    icon: SecurityScanOutlined,
    query: '请对网络进行全面的安全威胁扫描，识别潜在的安全风险和漏洞'
  },
  {
    key: 'performance-analysis',
    label: '性能瓶颈分析',
    description: '分析网络性能瓶颈和优化建议',
    icon: LineChartOutlined,
    query: '请分析网络性能指标，识别性能瓶颈并提供优化建议'
  },
  {
    key: 'anomaly-detection',
    label: '异常行为检测',
    description: '检测网络中的异常行为和模式',
    icon: ExclamationCircleOutlined,
    query: '请检测网络中的异常行为模式，包括流量异常、设备异常等'
  }
])

// 历史表格列定义
const historyColumns = [
  { title: '标题', key: 'title', width: 200, ellipsis: true },
  { title: '分析类型', key: 'analysis_type', width: 120 },
  { title: '置信度', key: 'confidence_score', width: 100 },
  { title: '分析时间', key: 'analyzed_at', width: 150 },
  { title: '操作', key: 'actions', width: 150, fixed: 'right' }
]

// 分析结果操作按钮
const analysisResultActions = [
  {
    icon: h(DownloadOutlined),
    tooltip: '导出结果',
    onClick: (result: AnalysisResponse) => exportSingleResult(result)
  },
  {
    icon: h(ReloadOutlined),
    tooltip: '重新分析',
    onClick: (result: AnalysisResponse) => rerunAnalysis(result)
  }
]

// 计算属性
const getCurrentAgent = () => {
  return availableAgents.value.find(a => a.key === selectedAgent.value) || availableAgents.value[0]
}

const getAnalysisIcon = () => {
  const icons = {
    [AnalysisType.NETWORK_ANALYSIS]: NetworkOutlined,
    [AnalysisType.SECURITY_ANALYSIS]: SecurityScanOutlined,
    [AnalysisType.PERFORMANCE_ANALYSIS]: LineChartOutlined,
    [AnalysisType.ANOMALY_DETECTION]: ExclamationCircleOutlined
  }
  return icons[selectedAnalysisType.value] || NetworkOutlined
}

const getAnalysisTitle = () => {
  const titles = {
    [AnalysisType.NETWORK_ANALYSIS]: '网络分析',
    [AnalysisType.SECURITY_ANALYSIS]: '安全分析',
    [AnalysisType.PERFORMANCE_ANALYSIS]: '性能分析',
    [AnalysisType.ANOMALY_DETECTION]: '异常检测'
  }
  return titles[selectedAnalysisType.value] || '网络分析'
}

const getAnalysisTypeColor = () => {
  const colors = {
    [AnalysisType.NETWORK_ANALYSIS]: 'blue',
    [AnalysisType.SECURITY_ANALYSIS]: 'red',
    [AnalysisType.PERFORMANCE_ANALYSIS]: 'green',
    [AnalysisType.ANOMALY_DETECTION]: 'orange'
  }
  return colors[selectedAnalysisType.value] || 'blue'
}

const getAnalysisTypeName = (type: AnalysisType) => {
  const names = {
    [AnalysisType.NETWORK_ANALYSIS]: '网络分析',
    [AnalysisType.SECURITY_ANALYSIS]: '安全分析',
    [AnalysisType.PERFORMANCE_ANALYSIS]: '性能分析',
    [AnalysisType.ANOMALY_DETECTION]: '异常检测',
    [AnalysisType.GENERAL_QUERY]: '通用查询'
  }
  return names[type] || type
}

// 方法
const handleAnalysisTypeChange = () => {
  // 根据分析类型自动选择合适的Agent
  if (selectedAnalysisType.value === AnalysisType.SECURITY_ANALYSIS) {
    selectedAgent.value = 'security'
  } else if (selectedAnalysisType.value === AnalysisType.PERFORMANCE_ANALYSIS) {
    selectedAgent.value = 'performance'
  } else {
    selectedAgent.value = 'general'
  }
}

const handleAgentChange = () => {
  // Agent变更时的处理逻辑
  console.log('Agent changed to:', selectedAgent.value)
}

const handleTemplateClick = (template: AnalysisTemplate) => {
  analysisQuery.value = template.query
  showQuickTemplates.value = false
}

const handleAnalysisSubmit = async (value: string) => {
  if (!value.trim() || analyzing.value) return

  analyzing.value = true

  try {
    // 构建分析请求
    const analysisRequest: AnalysisRequest = {
      analysis_type: selectedAnalysisType.value,
      query: value,
      data: {
        agent_type: selectedAgent.value,
        settings: analysisSettings.value
      }
    }

    // 调用AI分析API
    const response = await aiApi.analyze(analysisRequest)

    // 添加分析结果
    if (response) {
      analysisResults.value.push({
        ...response,
        status: 'success'
      } as AnalysisResponse)

      // 滚动到底部
      await scrollToBottom()

      // 自动保存结果
      if (analysisSettings.value.autoSave) {
        await loadAnalysisHistory()
      }

      message.success('分析完成')
    }
  } catch (error: any) {
    message.error(`分析失败: ${error.message}`)

    // 添加错误结果
    analysisResults.value.push({
      id: Date.now(),
      analysis_type: selectedAnalysisType.value,
      title: '分析失败',
      summary: `分析过程中发生错误：${error.message}`,
      analyzed_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_active: false,
      status: 'error'
    } as AnalysisResponse)
  } finally {
    analyzing.value = false
    analysisQuery.value = ''
  }
}

const handleAnalysisClear = () => {
  analysisQuery.value = ''
}

const clearAnalysisResults = () => {
  analysisResults.value = []
}

const scrollToBottom = async () => {
  await nextTick()
  if (resultsContainer.value) {
    resultsContainer.value.scrollTop = resultsContainer.value.scrollHeight
  }
}

// 分析历史相关方法
const loadAnalysisHistory = async () => {
  loadingHistory.value = true
  try {
    const params: AnalysisResultsParams = {
      skip: (historyPagination.value.current - 1) * historyPagination.value.pageSize,
      limit: historyPagination.value.pageSize
    }

    if (historyFilters.value.analysis_type) {
      params.analysis_type = historyFilters.value.analysis_type
    }
    if (historyFilters.value.dateRange) {
      params.start_date = historyFilters.value.dateRange[0].format('YYYY-MM-DD')
      params.end_date = historyFilters.value.dateRange[1].format('YYYY-MM-DD')
    }

    const response = await aiApi.getAnalysisResults(params)
    analysisHistory.value = response.data || []
    historyPagination.value.total = response.total || 0
  } catch (error: any) {
    message.error(`加载分析历史失败: ${error.message}`)
  } finally {
    loadingHistory.value = false
  }
}

const refreshAnalysisHistory = () => {
  loadAnalysisHistory()
}

const handleHistoryTableChange = (pagination: any) => {
  historyPagination.value.current = pagination.current
  historyPagination.value.pageSize = pagination.pageSize
  loadAnalysisHistory()
}

const viewHistoryDetail = (record: AnalysisResponse) => {
  // 将历史记录添加到当前分析结果中显示
  analysisResults.value.push({
    ...record,
    status: 'success'
  })
  showAnalysisHistory.value = false
  scrollToBottom()
}

const rerunAnalysis = async (record: AnalysisResponse) => {
  if (record.query) {
    analysisQuery.value = record.query
    await handleAnalysisSubmit(record.query)
  }
}

// 工具函数
const formatAnalysisResult = (result: AnalysisResponse): string => {
  return result.summary || result.title || '分析结果'
}

const formatTimestamp = (timestamp: string): string => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

const formatProcessingTime = (time?: number): string => {
  if (!time) return '-'
  if (time < 1) return `${Math.round(time * 1000)}ms`
  return `${time.toFixed(2)}s`
}

const formatTokenUsage = (usage?: Record<string, any>): string => {
  if (!usage) return '-'
  return `${usage.total_tokens || 0} tokens`
}

const formatFindingValue = (value: any): string => {
  if (typeof value === 'string') return value
  if (typeof value === 'object') return JSON.stringify(value, null, 2)
  return String(value)
}

const formatRecommendationValue = (value: any): string => {
  if (typeof value === 'string') return value
  if (typeof value === 'object') return JSON.stringify(value, null, 2)
  return String(value)
}

const getConfidenceColor = (score: number): string => {
  if (score >= 0.8) return '#52c41a'
  if (score >= 0.6) return '#faad14'
  if (score >= 0.4) return '#fa8c16'
  return '#ff4d4f'
}

// 文件上传处理
const handleFileUpload = (file: File): boolean => {
  const reader = new FileReader()
  reader.onload = (e) => {
    const content = e.target?.result as string
    analysisQuery.value += `\n\n附加文件内容：\n${content}`
  }
  reader.readAsText(file)
  return false // 阻止自动上传
}

// 导出功能
const exportSingleResult = (result: AnalysisResponse) => {
  const content = `分析结果导出
标题: ${result.title}
分析类型: ${getAnalysisTypeName(result.analysis_type)}
分析时间: ${formatTimestamp(result.analyzed_at)}
置信度: ${result.confidence_score ? Math.round(result.confidence_score * 100) + '%' : '-'}

摘要:
${result.summary || '无'}

分析发现:
${result.findings ? JSON.stringify(result.findings, null, 2) : '无'}

建议措施:
${result.recommendations ? JSON.stringify(result.recommendations, null, 2) : '无'}
`

  const blob = new Blob([content], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `analysis-${result.id}-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

const exportAnalysisResults = () => {
  if (analysisResults.value.length === 0) {
    message.warning('没有可导出的分析结果')
    return
  }

  const content = analysisResults.value.map(result =>
    `分析ID: ${result.id}
标题: ${result.title}
分析类型: ${getAnalysisTypeName(result.analysis_type)}
分析时间: ${formatTimestamp(result.analyzed_at)}
置信度: ${result.confidence_score ? Math.round(result.confidence_score * 100) + '%' : '-'}
摘要: ${result.summary || '无'}
---`
  ).join('\n\n')

  const blob = new Blob([content], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `analysis-results-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

// 设置相关
const saveAnalysisSettings = () => {
  message.success('分析设置已保存')
  showAnalysisSettings.value = false
}

// 生命周期
onMounted(() => {
  // 初始化时加载分析历史
  loadAnalysisHistory()
})
</script>

<style scoped>
/* 使用项目统一的设计风格 */
.ai-analysis {
  padding: var(--spacing-lg);
  background: var(--bg-color);
  min-height: 100vh;
}

.analysis-container {
  height: calc(100vh - 200px);
}

.config-sidebar {
  height: 100%;
}

.config-card {
  height: 100%;
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
}

.analysis-type-section h4,
.agent-selection h4,
.quick-templates h4 {
  margin-bottom: var(--spacing-sm);
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.analysis-option,
.agent-option {
  display: flex;
  align-items: center;
}

.agent-info {
  flex: 1;
}

.agent-name {
  font-weight: 500;
  margin-bottom: 2px;
  color: var(--text-primary);
}

.agent-desc {
  font-size: 12px;
  color: var(--text-secondary);
}

.template-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.template-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-align: left;
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.template-button:hover {
  background-color: var(--border-color-light);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-light);
}

.analysis-main {
  height: 100%;
}

.analysis-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  border: 1px solid var(--border-color-light);
}

.analysis-header {
  display: flex;
  align-items: center;
  color: var(--text-primary);
}

.ai-analysis-container {
  height: calc(100vh - 350px);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
}

.analysis-results-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md) 0;
  max-height: calc(100vh - 450px);
  border-radius: var(--border-radius);
  background: var(--bg-color);
}

.analysis-result-bubble {
  margin-bottom: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
  transition: all 0.2s ease;
}

.analysis-result-bubble:hover {
  box-shadow: var(--box-shadow);
  transform: translateY(-1px);
}

.analysis-result-content {
  padding: var(--spacing-lg);
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
}

.result-header {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color-light);
}

.result-header h4 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.result-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.result-time {
  font-size: 12px;
  color: var(--text-secondary);
}

.result-summary,
.result-findings,
.result-recommendations {
  margin-bottom: var(--spacing-md);
}

.result-summary h5,
.result-findings h5,
.result-recommendations h5 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.result-summary h5::before {
  content: "📋";
}

.result-findings h5::before {
  content: "🔍";
}

.result-recommendations h5::before {
  content: "💡";
}

.findings-content,
.recommendations-content {
  background: var(--border-color-light);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.findings-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: var(--text-primary);
}

.finding-item,
.recommendation-item {
  margin-bottom: var(--spacing-sm);
  padding: var(--spacing-xs);
  border-radius: var(--border-radius);
  background: var(--card-bg);
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
}

.result-processing-info {
  border-top: 1px solid var(--border-color-light);
  padding-top: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  border: 2px dashed var(--border-color);
  margin: var(--spacing-lg);
}

.analysis-input-prompt {
  border-top: 1px solid var(--border-color-light);
  padding-top: var(--spacing-md);
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-light);
}

.quick-templates-overlay {
  position: absolute;
  bottom: 100px;
  left: 0;
  right: 0;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  z-index: 1000;
  backdrop-filter: blur(8px);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.templates-content h4 {
  margin-bottom: var(--spacing-md);
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.templates-content h4::before {
  content: "⚡";
  font-size: 18px;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-md);
}

.template-card {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
  height: auto;
  background: var(--card-bg);
  cursor: pointer;
}

.template-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--box-shadow);
  transform: translateY(-2px);
}

.template-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  text-align: left;
}

.template-icon {
  font-size: 24px;
  color: var(--primary-color);
  margin-top: 2px;
  background: rgba(24, 144, 255, 0.1);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
}

.template-text {
  flex: 1;
}

.template-title {
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
  font-size: 14px;
}

.template-desc {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.analysis-history-content {
  max-height: 70vh;
  overflow-y: auto;
  background: var(--bg-color);
  border-radius: var(--border-radius);
}

.history-filters {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--card-bg);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color-light);
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--spacing-xs);
}

.status-success {
  background-color: var(--success-color);
}

.status-error {
  background-color: var(--error-color);
}

.status-loading {
  background-color: var(--warning-color);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .templates-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .ai-analysis {
    padding: var(--spacing-md);
  }

  .analysis-container {
    height: auto;
  }

  .config-sidebar {
    margin-bottom: var(--spacing-md);
  }

  .ai-analysis-container {
    height: calc(100vh - 300px);
    padding: var(--spacing-md);
  }

  .analysis-results-container {
    max-height: calc(100vh - 400px);
    padding: var(--spacing-sm) 0;
  }

  .analysis-result-content {
    padding: var(--spacing-md);
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }

  .quick-templates-overlay {
    bottom: 80px;
    left: var(--spacing-sm);
    right: var(--spacing-sm);
    padding: var(--spacing-md);
  }

  .result-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .template-content {
    gap: var(--spacing-sm);
  }

  .template-icon {
    font-size: 20px;
    padding: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  .ai-analysis {
    padding: var(--spacing-sm);
  }

  .analysis-result-content {
    padding: var(--spacing-sm);
  }

  .result-header h4 {
    font-size: 14px;
  }

  .findings-content,
  .recommendations-content {
    font-size: 12px;
    padding: var(--spacing-sm);
  }

  .quick-templates-overlay {
    bottom: 60px;
  }
}

/* 深色主题适配 */
[data-theme='dark'] .analysis-result-bubble {
  border: 1px solid var(--border-color);
}

[data-theme='dark'] .findings-content,
[data-theme='dark'] .recommendations-content {
  background: var(--border-color-light);
  border-color: var(--border-color);
}

[data-theme='dark'] .template-icon {
  background: rgba(24, 144, 255, 0.2);
}

/* 打印样式 */
@media print {
  .ai-analysis {
    padding: 0;
    background: white;
  }

  .config-sidebar,
  .quick-templates-overlay,
  .analysis-input-prompt {
    display: none;
  }

  .analysis-result-bubble {
    box-shadow: none;
    border: 1px solid #ddd;
    margin-bottom: 20px;
    page-break-inside: avoid;
  }
}
</style>

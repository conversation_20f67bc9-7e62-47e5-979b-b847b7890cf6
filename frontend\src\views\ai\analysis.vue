<template>
  <div class="ai-analysis">
    <!-- Ant Design X Provider -->
    <ax-provider :theme="xTheme">
      <a-row :gutter="16" class="analysis-container">
        <!-- Analysis Tools Sidebar -->
        <a-col :xs="24" :md="6" class="tools-sidebar">
          <a-card title="分析工具" size="small">
            <!-- Analysis Type Selection -->
            <div class="analysis-type">
              <h4>分析类型</h4>
              <a-radio-group v-model:value="analysisType" @change="handleAnalysisTypeChange">
                <a-radio-button value="network">网络分析</a-radio-button>
                <a-radio-button value="security">安全分析</a-radio-button>
                <a-radio-button value="performance">性能分析</a-radio-button>
              </a-radio-group>
            </div>

            <a-divider />

            <!-- Quick Analysis Prompts -->
            <div class="quick-analysis">
              <h4>快速分析</h4>
              <ax-prompts
                :items="analysisPrompts"
                @click="handleAnalysisPromptClick"
                class="analysis-prompts"
              />
            </div>

            <a-divider />

            <!-- Analysis History -->
            <div class="analysis-history">
              <h4>分析历史</h4>
              <ax-conversations
                :items="conversationItems"
                :active-key="activeConversation"
                @change="handleConversationChange"
                style="height: 200px;"
              />
            </div>
          </a-card>
        </a-col>

        <!-- Analysis Interface -->
        <a-col :xs="24" :md="18" class="analysis-main">
          <a-card class="analysis-card">
            <!-- Analysis Header -->
            <template #title>
              <div class="analysis-header">
                <a-avatar :icon="h(getAnalysisIcon())" />
                <div class="analysis-info">
                  <div class="analysis-name">{{ getAnalysisTitle() }}</div>
                  <div class="analysis-desc">{{ getAnalysisDescription() }}</div>
                </div>
              </div>
            </template>

            <template #extra>
              <a-space>
                <a-button
                  type="text"
                  :icon="h(BarChartOutlined)"
                  @click="generateReport"
                  title="生成报告"
                />
                <a-button
                  type="text"
                  :icon="h(DownloadOutlined)"
                  @click="exportAnalysis"
                  title="导出分析"
                />
              </a-space>
            </template>

            <!-- Analysis Interface -->
            <div class="analysis-interface">
              <!-- Analysis Results using Bubble.List -->
              <ax-bubble-list
                :items="analysisResults"
                class="analysis-results"
                ref="resultsContainer"
              />

              <!-- Empty State -->
              <div v-if="analysisResults.length === 0" class="empty-state">
                <a-empty description="开始AI分析吧！">
                  <template #image>
                    <BarChartOutlined style="font-size: 64px; color: #d9d9d9;" />
                  </template>
                </a-empty>
              </div>

              <!-- Thought Chain for Analysis Process -->
              <ax-thought-chain
                v-if="showThoughtChain"
                :items="thoughtChainItems"
                class="thought-chain"
              />

              <!-- Analysis Input with Attachments -->
              <div class="analysis-input-section">
                <ax-attachments
                  v-if="attachments.length > 0"
                  :items="attachments"
                  @remove="handleRemoveAttachment"
                  class="analysis-attachments"
                />

                <ax-sender
                  v-model:value="analysisQuery"
                  :placeholder="`输入${getAnalysisTitle()}查询...`"
                  :loading="analyzing"
                  @submit="handleAnalysisSubmit"
                  class="analysis-sender"
                  :actions="{
                    clear: true,
                    send: true,
                    attachment: true
                  }"
                  @attachment="handleAttachment"
                />
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </ax-provider>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, h } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { 
  XProvider as AxProvider,
  Bubble as AxBubble, 
  Sender as AxSender, 
  Prompts as AxPrompts,
  Conversations as AxConversations,
  ThoughtChain as AxThoughtChain,
  Attachments as AxAttachments
} from 'ant-design-x-vue'
import { aiApi } from '@/api/ai'
import {
  BarChartOutlined,
  SecurityScanOutlined,
  LineChartOutlined,
  NetworkOutlined,
  DownloadOutlined,
  FileTextOutlined,
  BulbOutlined,
  AlertOutlined,
  RobotOutlined
} from '@ant-design/icons-vue'

// Types
interface AnalysisResult {
  key: string
  content: string
  placement?: 'start' | 'end'
  avatar?: {
    src?: string
    icon?: any
  }
  loading?: boolean
  type?: 'text' | 'chart' | 'table'
}

interface PromptItem {
  key: string
  label: string
  icon?: any
}

interface ConversationItem {
  key: string
  label: string
  icon?: any
}

interface ThoughtChainItem {
  key: string
  title: string
  description?: string
  status?: 'pending' | 'processing' | 'success' | 'error'
}

interface AttachmentItem {
  key: string
  name: string
  type: string
  size?: number
  url?: string
}

// Ant Design X Theme Configuration
const xTheme = {
  token: {
    colorPrimary: '#1890ff',
    borderRadius: 8
  }
}

// State
const analysisType = ref('network')
const analysisQuery = ref('')
const analyzing = ref(false)
const analysisResults = ref<AnalysisResult[]>([])
const resultsContainer = ref<HTMLElement>()
const showThoughtChain = ref(false)
const activeConversation = ref('1')

// Attachments
const attachments = ref<AttachmentItem[]>([])

// Analysis prompts for different types
const analysisPrompts = ref<PromptItem[]>([
  {
    key: 'network-topology',
    label: '分析网络拓扑',
    icon: h(NetworkOutlined)
  },
  {
    key: 'traffic-analysis',
    label: '流量分析',
    icon: h(LineChartOutlined)
  },
  {
    key: 'security-scan',
    label: '安全扫描',
    icon: h(SecurityScanOutlined)
  },
  {
    key: 'performance-report',
    label: '性能报告',
    icon: h(BarChartOutlined)
  }
])

// Conversation items
const conversationItems = ref<ConversationItem[]>([
  {
    key: '1',
    label: '网络分析 - 今日',
    icon: h(NetworkOutlined)
  },
  {
    key: '2',
    label: '安全分析 - 昨日',
    icon: h(SecurityScanOutlined)
  },
  {
    key: '3',
    label: '性能分析 - 本周',
    icon: h(LineChartOutlined)
  }
])

// Thought chain items
const thoughtChainItems = ref<ThoughtChainItem[]>([
  {
    key: '1',
    title: '数据收集',
    description: '正在收集网络监控数据...',
    status: 'processing'
  },
  {
    key: '2',
    title: '数据分析',
    description: '使用AI算法分析数据模式...',
    status: 'pending'
  },
  {
    key: '3',
    title: '生成报告',
    description: '生成分析报告和建议...',
    status: 'pending'
  }
])

// Methods
const getAnalysisIcon = () => {
  const icons = {
    network: NetworkOutlined,
    security: SecurityScanOutlined,
    performance: LineChartOutlined
  }
  return icons[analysisType.value as keyof typeof icons] || NetworkOutlined
}

const getAnalysisTitle = () => {
  const titles = {
    network: '网络分析',
    security: '安全分析',
    performance: '性能分析'
  }
  return titles[analysisType.value as keyof typeof titles] || '网络分析'
}

const getAnalysisDescription = () => {
  const descriptions = {
    network: '分析网络拓扑、连接状态和流量模式',
    security: '检测安全威胁、漏洞和异常行为',
    performance: '监控性能指标、瓶颈和优化建议'
  }
  return descriptions[analysisType.value as keyof typeof descriptions] || '网络分析'
}

const handleAnalysisTypeChange = () => {
  // Update prompts based on analysis type
  updateAnalysisPrompts()
}

const updateAnalysisPrompts = () => {
  const promptsMap = {
    network: [
      { key: 'network-topology', label: '分析网络拓扑', icon: h(NetworkOutlined) },
      { key: 'traffic-analysis', label: '流量分析', icon: h(LineChartOutlined) },
      { key: 'connectivity-check', label: '连接性检查', icon: h(BulbOutlined) }
    ],
    security: [
      { key: 'security-scan', label: '安全扫描', icon: h(SecurityScanOutlined) },
      { key: 'threat-detection', label: '威胁检测', icon: h(AlertOutlined) },
      { key: 'vulnerability-assessment', label: '漏洞评估', icon: h(FileTextOutlined) }
    ],
    performance: [
      { key: 'performance-report', label: '性能报告', icon: h(BarChartOutlined) },
      { key: 'bottleneck-analysis', label: '瓶颈分析', icon: h(LineChartOutlined) },
      { key: 'optimization-suggestions', label: '优化建议', icon: h(BulbOutlined) }
    ]
  }
  
  analysisPrompts.value = promptsMap[analysisType.value as keyof typeof promptsMap] || promptsMap.network
}

const handleAnalysisPromptClick = (item: PromptItem) => {
  analysisQuery.value = item.label
  handleAnalysisSubmit()
}

const handleConversationChange = (key: string) => {
  activeConversation.value = key
  // Load conversation history
  loadConversationHistory(key)
}

const loadConversationHistory = (conversationId: string) => {
  // Mock loading conversation history
  console.log('Loading conversation:', conversationId)
}

const handleAnalysisSubmit = async () => {
  if (!analysisQuery.value.trim() || analyzing.value) return

  const userQuery: AnalysisResult = {
    key: Date.now().toString(),
    content: analysisQuery.value,
    placement: 'end',
    avatar: { icon: h(RobotOutlined) }
  }

  analysisResults.value.push(userQuery)

  // Show thought chain
  showThoughtChain.value = true
  
  // Create loading result
  const loadingResult: AnalysisResult = {
    key: (Date.now() + 1).toString(),
    content: 'AI正在分析中...',
    placement: 'start',
    avatar: { icon: h(getAnalysisIcon()) },
    loading: true
  }

  analysisResults.value.push(loadingResult)
  analysisQuery.value = ''
  analyzing.value = true

  await scrollToBottom()

  try {
    // Simulate analysis process
    await simulateAnalysisProcess()
    
    // Remove loading result
    analysisResults.value = analysisResults.value.filter(r => r.key !== loadingResult.key)

    // Add analysis result
    const analysisResult: AnalysisResult = {
      key: Date.now().toString(),
      content: `基于${getAnalysisTitle()}的结果：\n\n✅ 分析完成\n📊 发现3个关键指标\n⚠️ 检测到2个潜在问题\n💡 提供5条优化建议`,
      placement: 'start',
      avatar: { icon: h(getAnalysisIcon()) }
    }

    analysisResults.value.push(analysisResult)

  } catch (error: any) {
    // Remove loading result
    analysisResults.value = analysisResults.value.filter(r => r.key !== loadingResult.key)

    // Add error result
    const errorResult: AnalysisResult = {
      key: Date.now().toString(),
      content: `分析失败：${error.message || '未知错误'}`,
      placement: 'start',
      avatar: { icon: h(AlertOutlined) }
    }

    analysisResults.value.push(errorResult)
    message.error('分析失败')
  } finally {
    analyzing.value = false
    showThoughtChain.value = false
    await scrollToBottom()
  }
}

const simulateAnalysisProcess = async () => {
  // Simulate thought chain progress
  for (let i = 0; i < thoughtChainItems.value.length; i++) {
    thoughtChainItems.value[i].status = 'processing'
    await new Promise(resolve => setTimeout(resolve, 1000))
    thoughtChainItems.value[i].status = 'success'
  }
}

const handleAttachment = () => {
  // Mock file attachment
  const newAttachment: AttachmentItem = {
    key: Date.now().toString(),
    name: `network_data_${Date.now()}.csv`,
    type: 'csv',
    size: 1024 * 1024 // 1MB
  }
  attachments.value.push(newAttachment)
}

const handleRemoveAttachment = (key: string) => {
  attachments.value = attachments.value.filter(item => item.key !== key)
}

const scrollToBottom = async () => {
  await nextTick()
  if (resultsContainer.value) {
    resultsContainer.value.scrollTop = resultsContainer.value.scrollHeight
  }
}

const generateReport = () => {
  message.info('正在生成分析报告...')
}

const exportAnalysis = () => {
  const analysisContent = analysisResults.value
    .filter(r => !r.loading)
    .map(r => r.content)
    .join('\n\n')

  const blob = new Blob([analysisContent], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${getAnalysisTitle()}-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

// Lifecycle
onMounted(() => {
  updateAnalysisPrompts()
})
</script>

<style scoped>
.ai-analysis {
  padding: 24px;
  background: var(--bg-color);
  min-height: 100vh;
}

.analysis-container {
  height: calc(100vh - 48px);
}

.tools-sidebar {
  height: 100%;
}

.analysis-type h4 {
  margin-bottom: 12px;
  font-size: 14px;
}

.quick-analysis h4,
.analysis-history h4 {
  margin-bottom: 12px;
  font-size: 14px;
}

.analysis-main {
  height: 100%;
}

.analysis-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.analysis-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.analysis-header .analysis-info .analysis-name {
  font-weight: 500;
}

.analysis-results {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  max-height: calc(100vh - 400px);
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.thought-chain {
  margin: 16px 0;
}

.analysis-input-section {
  border-top: 1px solid var(--border-color-light);
  padding-top: 16px;
}

.analysis-attachments {
  margin-bottom: 12px;
}

/* Responsive */
@media (max-width: 768px) {
  .ai-analysis {
    padding: 16px;
  }
  
  .analysis-container {
    height: auto;
  }
  
  .tools-sidebar {
    margin-bottom: 16px;
  }
}
</style>

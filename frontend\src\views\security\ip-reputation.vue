<template>
  <div class="ip-reputation-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>IP信誉查询</h1>
      <a-button @click="$router.back()">
        <template #icon><ArrowLeftOutlined /></template>
        返回
      </a-button>
    </div>

    <!-- 查询表单 -->
    <a-card title="IP地址查询" class="query-card">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <!-- 单个IP查询 -->
        <a-tab-pane key="single" tab="单个查询">
          <a-form layout="inline" @finish="handleSingleQuery">
            <a-form-item label="IP地址" name="ip" :rules="[{ required: true, message: '请输入IP地址' }]">
              <a-input
                v-model:value="singleIP"
                placeholder="请输入IP地址，如：*************"
                style="width: 300px;"
                @pressEnter="handleSingleQuery"
              />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" html-type="submit" :loading="singleLoading">
                <template #icon><SearchOutlined /></template>
                查询
              </a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>

        <!-- 批量IP查询 -->
        <a-tab-pane key="batch" tab="批量查询">
          <a-form @finish="handleBatchQuery">
            <a-form-item label="IP地址列表">
              <a-textarea
                v-model:value="batchIPs"
                placeholder="请输入多个IP地址，支持换行、逗号、空格分隔&#10;例如：&#10;*************&#10;*************, *************&#10;************* *************"
                :rows="6"
                style="width: 100%;"
              />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" html-type="submit" :loading="batchLoading">
                <template #icon><SearchOutlined /></template>
                批量查询
              </a-button>
              <a-button style="margin-left: 8px;" @click="clearBatchInput">
                清空
              </a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 单个查询结果 -->
    <a-card v-if="singleResult" title="查询结果" class="result-card">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="IP地址">
          <a-typography-text copyable>{{ singleResult.ip_address }}</a-typography-text>
        </a-descriptions-item>
        <a-descriptions-item label="威胁状态">
          <a-tag :color="singleResult.is_malicious ? 'red' : 'green'" size="large">
            {{ singleResult.is_malicious ? '🚨 恶意' : '✅ 安全' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="威胁级别">
          <a-tag :color="getThreatLevelColor(singleResult.threat_level)" size="large">
            {{ getThreatLevelLabel(singleResult.threat_level) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="置信度">
          <a-progress
            :percent="Math.round(singleResult.confidence * 100)"
            :stroke-color="getConfidenceColor(singleResult.confidence)"
            style="width: 200px;"
          />
        </a-descriptions-item>
        <a-descriptions-item label="威胁原因" :span="2">
          {{ singleResult.reason || '无特殊威胁' }}
        </a-descriptions-item>
        <a-descriptions-item label="威胁类别" :span="2">
          <a-tag v-for="category in singleResult.categories" :key="category" color="blue">
            {{ category }}
          </a-tag>
          <span v-if="!singleResult.categories || singleResult.categories.length === 0">无</span>
        </a-descriptions-item>
        <a-descriptions-item label="数据来源">
          {{ singleResult.source }}
        </a-descriptions-item>
        <a-descriptions-item label="最后发现">
          {{ formatTime(singleResult.last_seen) }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 操作按钮 -->
      <div class="result-actions">
        <a-button
          v-if="singleResult.is_malicious"
          type="primary"
          danger
          @click="addToBlacklist(singleResult.ip_address, singleResult.reason || '威胁IP')"
          :loading="addingToBlacklist"
        >
          <template #icon><StopOutlined /></template>
          添加到黑名单
        </a-button>
        <a-button @click="exportSingleResult">
          <template #icon><DownloadOutlined /></template>
          导出结果
        </a-button>
      </div>
    </a-card>

    <!-- 批量查询结果 -->
    <a-card v-if="batchResults.length > 0" title="批量查询结果" class="result-card">
      <template #extra>
        <a-space>
          <a-button @click="exportBatchResults">
            <template #icon><DownloadOutlined /></template>
            导出全部
          </a-button>
          <a-button @click="addMaliciousToBlacklist" :loading="batchAddingToBlacklist">
            <template #icon><StopOutlined /></template>
            恶意IP加入黑名单
          </a-button>
        </a-space>
      </template>

      <!-- 统计信息 -->
      <a-row :gutter="16" class="batch-stats">
        <a-col :span="6">
          <a-statistic title="总查询数" :value="batchResults.length" />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="恶意IP"
            :value="batchResults.filter(r => r.is_malicious).length"
            :value-style="{ color: '#ff4d4f' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="安全IP"
            :value="batchResults.filter(r => !r.is_malicious).length"
            :value-style="{ color: '#52c41a' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="高危威胁"
            :value="batchResults.filter(r => r.threat_level === 'high' || r.threat_level === 'critical').length"
            :value-style="{ color: '#fa8c16' }"
          />
        </a-col>
      </a-row>

      <!-- 结果表格 -->
      <a-table
        :columns="batchColumns"
        :data-source="batchResults"
        :pagination="{ pageSize: 10 }"
        size="small"
        class="batch-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'ip_address'">
            <a-typography-text copyable>{{ record.ip_address }}</a-typography-text>
          </template>
          <template v-else-if="column.key === 'is_malicious'">
            <a-tag :color="record.is_malicious ? 'red' : 'green'">
              {{ record.is_malicious ? '恶意' : '安全' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'threat_level'">
            <a-tag :color="getThreatLevelColor(record.threat_level)">
              {{ getThreatLevelLabel(record.threat_level) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'confidence'">
            <a-progress
              :percent="Math.round(record.confidence * 100)"
              size="small"
              :stroke-color="getConfidenceColor(record.confidence)"
            />
          </template>
          <template v-else-if="column.key === 'categories'">
            <a-tag v-for="category in record.categories" :key="category" size="small">
              {{ category }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button
                v-if="record.is_malicious"
                size="small"
                type="primary"
                danger
                @click="addToBlacklist(record.ip_address, record.reason || '威胁IP')"
              >
                加入黑名单
              </a-button>
              <a-button size="small" @click="viewDetails(record)">
                详情
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="IP详细信息"
      :footer="null"
      width="600px"
    >
      <a-descriptions v-if="selectedRecord" :column="1" bordered>
        <a-descriptions-item label="IP地址">
          <a-typography-text copyable>{{ selectedRecord.ip_address }}</a-typography-text>
        </a-descriptions-item>
        <a-descriptions-item label="威胁状态">
          <a-tag :color="selectedRecord.is_malicious ? 'red' : 'green'">
            {{ selectedRecord.is_malicious ? '恶意' : '安全' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="威胁级别">
          <a-tag :color="getThreatLevelColor(selectedRecord.threat_level)">
            {{ getThreatLevelLabel(selectedRecord.threat_level) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="置信度">
          {{ Math.round(selectedRecord.confidence * 100) }}%
        </a-descriptions-item>
        <a-descriptions-item label="威胁原因">
          {{ selectedRecord.reason || '无特殊威胁' }}
        </a-descriptions-item>
        <a-descriptions-item label="威胁类别">
          <a-tag v-for="category in selectedRecord.categories" :key="category">
            {{ category }}
          </a-tag>
          <span v-if="!selectedRecord.categories || selectedRecord.categories.length === 0">无</span>
        </a-descriptions-item>
        <a-descriptions-item label="数据来源">
          {{ selectedRecord.source }}
        </a-descriptions-item>
        <a-descriptions-item label="最后发现">
          {{ formatTime(selectedRecord.last_seen) }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  SearchOutlined,
  StopOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import { threatApi, threatUtils, type IPReputationResponse } from '@/api/threat'

// 响应式数据
const activeTab = ref('single')
const singleIP = ref('')
const batchIPs = ref('')
const singleLoading = ref(false)
const batchLoading = ref(false)
const addingToBlacklist = ref(false)
const batchAddingToBlacklist = ref(false)

const singleResult = ref<IPReputationResponse | null>(null)
const batchResults = ref<IPReputationResponse[]>([])

const detailModalVisible = ref(false)
const selectedRecord = ref<IPReputationResponse | null>(null)

// 表格列定义
const batchColumns = [
  { title: 'IP地址', dataIndex: 'ip_address', key: 'ip_address', width: 140 },
  { title: '威胁状态', dataIndex: 'is_malicious', key: 'is_malicious', width: 80 },
  { title: '威胁级别', dataIndex: 'threat_level', key: 'threat_level', width: 100 },
  { title: '置信度', dataIndex: 'confidence', key: 'confidence', width: 120 },
  { title: '威胁原因', dataIndex: 'reason', key: 'reason', ellipsis: true },
  { title: '来源', dataIndex: 'source', key: 'source', width: 100 },
  { title: '操作', key: 'actions', width: 150 }
]

// 工具函数
const getThreatLevelColor = (level: string) => {
  const info = threatUtils.getThreatLevelInfo(level)
  return info.color
}

const getThreatLevelLabel = (level: string) => {
  const info = threatUtils.getThreatLevelInfo(level)
  return info.label
}

const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.8) return '#52c41a'
  if (confidence >= 0.6) return '#faad14'
  return '#ff4d4f'
}

const formatTime = (timestamp?: string) => {
  return threatUtils.formatTimestamp(timestamp)
}

// 事件处理
const handleTabChange = (key: string) => {
  // 切换标签时清空结果
  if (key === 'single') {
    batchResults.value = []
  } else {
    singleResult.value = null
  }
}

const handleSingleQuery = async () => {
  if (!singleIP.value.trim()) {
    message.error('请输入IP地址')
    return
  }

  if (!threatUtils.validateIP(singleIP.value.trim())) {
    message.error('请输入有效的IP地址')
    return
  }

  try {
    singleLoading.value = true
    const response = await threatApi.checkIpReputation({ ip_address: singleIP.value.trim() })
    singleResult.value = response.data
    message.success('查询完成')
  } catch (error) {
    console.error('IP信誉查询失败:', error)
    message.error('查询失败，请稍后重试')
  } finally {
    singleLoading.value = false
  }
}

const handleBatchQuery = async () => {
  if (!batchIPs.value.trim()) {
    message.error('请输入IP地址列表')
    return
  }

  const ipList = threatUtils.parseIPList(batchIPs.value)
  if (ipList.length === 0) {
    message.error('未找到有效的IP地址')
    return
  }

  if (ipList.length > 100) {
    message.error('单次最多支持查询100个IP地址')
    return
  }

  try {
    batchLoading.value = true
    const response = await threatApi.batchCheckIps(ipList)
    batchResults.value = Object.values(response.data.results)
    message.success(`批量查询完成，共查询${response.data.total_checked}个IP`)
  } catch (error) {
    console.error('批量IP查询失败:', error)
    message.error('批量查询失败，请稍后重试')
  } finally {
    batchLoading.value = false
  }
}

const clearBatchInput = () => {
  batchIPs.value = ''
  batchResults.value = []
}

const addToBlacklist = async (ip: string, reason: string) => {
  try {
    addingToBlacklist.value = true
    await threatApi.addToBlacklist({
      ip_address: ip,
      reason: reason,
      severity: 'high'
    })
    message.success(`IP ${ip} 已添加到黑名单`)
  } catch (error) {
    console.error('添加黑名单失败:', error)
    message.error('添加黑名单失败')
  } finally {
    addingToBlacklist.value = false
  }
}

const addMaliciousToBlacklist = async () => {
  const maliciousIPs = batchResults.value.filter(r => r.is_malicious)
  if (maliciousIPs.length === 0) {
    message.info('没有恶意IP需要添加到黑名单')
    return
  }

  try {
    batchAddingToBlacklist.value = true
    const promises = maliciousIPs.map(ip =>
      threatApi.addToBlacklist({
        ip_address: ip.ip_address,
        reason: ip.reason || '批量检测发现的威胁IP',
        severity: ip.threat_level === 'critical' ? 'critical' : 'high'
      })
    )
    await Promise.all(promises)
    message.success(`已将${maliciousIPs.length}个恶意IP添加到黑名单`)
  } catch (error) {
    console.error('批量添加黑名单失败:', error)
    message.error('批量添加黑名单失败')
  } finally {
    batchAddingToBlacklist.value = false
  }
}

const viewDetails = (record: IPReputationResponse) => {
  selectedRecord.value = record
  detailModalVisible.value = true
}

const exportSingleResult = () => {
  if (!singleResult.value) return
  
  const data = JSON.stringify(singleResult.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `ip_reputation_${singleResult.value.ip_address}.json`
  a.click()
  URL.revokeObjectURL(url)
  message.success('导出成功')
}

const exportBatchResults = () => {
  if (batchResults.value.length === 0) return
  
  const data = JSON.stringify(batchResults.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `batch_ip_reputation_${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  message.success('导出成功')
}
</script>

<style scoped>
.ip-reputation-page {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.query-card {
  margin-bottom: 24px;
}

.result-card {
  margin-bottom: 24px;
}

.result-actions {
  margin-top: 16px;
  text-align: right;
}

.result-actions .ant-btn {
  margin-left: 8px;
}

.batch-stats {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.batch-table {
  margin-top: 16px;
}
</style>

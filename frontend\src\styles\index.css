/* CampusGuard Global Styles */

/* CSS Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom CSS Variables */
:root {
  /* Colors */
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --info-color: #1890ff;
  
  /* Background Colors */
  --bg-color: #f0f2f5;
  --card-bg: #ffffff;
  --header-bg: #001529;
  --sidebar-bg: #001529;
  
  /* Text Colors */
  --text-primary: rgba(0, 0, 0, 0.85);
  --text-secondary: rgba(0, 0, 0, 0.65);
  --text-disabled: rgba(0, 0, 0, 0.25);
  --text-white: #ffffff;
  
  /* Border Colors */
  --border-color: #d9d9d9;
  --border-color-light: #f0f0f0;
  
  /* Shadow */
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  --box-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.12);
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Border Radius */
  --border-radius: 6px;
  --border-radius-lg: 8px;
  
  /* Z-index */
  --z-header: 1000;
  --z-sidebar: 999;
  --z-modal: 1050;
  --z-tooltip: 1060;
}

/* Dark theme variables */
[data-theme='dark'] {
  --bg-color: #141414;
  --card-bg: #1f1f1f;
  --header-bg: #1f1f1f;
  --sidebar-bg: #1f1f1f;
  
  --text-primary: rgba(255, 255, 255, 0.85);
  --text-secondary: rgba(255, 255, 255, 0.65);
  --text-disabled: rgba(255, 255, 255, 0.25);
  
  --border-color: #434343;
  --border-color-light: #303030;
}

/* Utility Classes */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

/* Custom Components */
.page-container {
  padding: var(--spacing-lg);
  background: var(--bg-color);
  min-height: 100vh;
}

.card {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--spacing-sm);
}

.status-online {
  background-color: var(--success-color);
}

.status-offline {
  background-color: var(--error-color);
}

.status-warning {
  background-color: var(--warning-color);
}

.status-unknown {
  background-color: var(--text-disabled);
}

/* Responsive */
@media (max-width: 768px) {
  .page-container {
    padding: var(--spacing-md);
  }
  
  .card {
    padding: var(--spacing-md);
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--border-color-light);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Animation */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

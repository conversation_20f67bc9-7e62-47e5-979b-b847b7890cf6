# CampusGuard Documentation

## Project Structure

```
campusguard/
├── backend/           # FastAPI backend application
├── frontend/          # Vue.js frontend application  
├── config/           # Configuration files
├── logs/             # Application logs
├── docs/             # Documentation
├── scripts/          # Utility scripts
└── tests/            # Integration tests
```

## Getting Started

1. Set up Python virtual environment
2. Install backend dependencies
3. Configure environment variables
4. Set up database
5. Install frontend dependencies
6. Start development servers

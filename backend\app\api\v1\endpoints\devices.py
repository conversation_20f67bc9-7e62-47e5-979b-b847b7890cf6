"""
Device management endpoints
"""
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from loguru import logger

from ....core.database import get_db
from ....models.device import Device, DeviceType, DeviceStatus
from ....schemas.device import DeviceCreate, DeviceUpdate, DeviceResponse, DeviceStats

router = APIRouter()


@router.get("/", response_model=List[DeviceResponse])
async def get_devices(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    device_type: Optional[DeviceType] = None,
    status: Optional[DeviceStatus] = None,
    db: Session = Depends(get_db)
):
    """Get all devices with optional filtering"""
    try:
        query = db.query(Device).filter(Device.is_active == True)
        
        if device_type:
            query = query.filter(Device.device_type == device_type)
        if status:
            query = query.filter(Device.status == status)
            
        devices = query.offset(skip).limit(limit).all()
        return devices
        
    except Exception as e:
        logger.error(f"Error getting devices: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve devices")


@router.get("/{device_id}", response_model=DeviceResponse)
async def get_device(device_id: int, db: Session = Depends(get_db)):
    """Get device by ID"""
    try:
        device = db.query(Device).filter(
            Device.id == device_id,
            Device.is_active == True
        ).first()
        
        if not device:
            raise HTTPException(status_code=404, detail="Device not found")
            
        return device
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting device {device_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve device")


@router.post("/", response_model=DeviceResponse)
async def create_device(device: DeviceCreate, db: Session = Depends(get_db)):
    """Create new device"""
    try:
        # Check if device with same IP already exists
        existing = db.query(Device).filter(
            Device.ip_address == device.ip_address,
            Device.is_active == True
        ).first()
        
        if existing:
            raise HTTPException(
                status_code=400, 
                detail=f"Device with IP {device.ip_address} already exists"
            )
        
        # Create new device
        db_device = Device(**device.model_dump())
        db.add(db_device)
        db.commit()
        db.refresh(db_device)
        
        logger.info(f"Created device: {db_device.name} ({db_device.ip_address})")
        return db_device
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating device: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create device")


@router.put("/{device_id}", response_model=DeviceResponse)
async def update_device(
    device_id: int, 
    device_update: DeviceUpdate, 
    db: Session = Depends(get_db)
):
    """Update device"""
    try:
        device = db.query(Device).filter(
            Device.id == device_id,
            Device.is_active == True
        ).first()
        
        if not device:
            raise HTTPException(status_code=404, detail="Device not found")
        
        # Update device fields
        update_data = device_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(device, field, value)
        
        db.commit()
        db.refresh(device)
        
        logger.info(f"Updated device: {device.name} ({device.ip_address})")
        return device
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating device {device_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update device")


@router.delete("/{device_id}")
async def delete_device(device_id: int, db: Session = Depends(get_db)):
    """Delete device (soft delete)"""
    try:
        device = db.query(Device).filter(
            Device.id == device_id,
            Device.is_active == True
        ).first()
        
        if not device:
            raise HTTPException(status_code=404, detail="Device not found")
        
        # Soft delete
        device.is_active = False
        db.commit()
        
        logger.info(f"Deleted device: {device.name} ({device.ip_address})")
        return {"message": "Device deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting device {device_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete device")


@router.get("/stats", response_model=DeviceStats)
async def get_device_stats(db: Session = Depends(get_db)):
    """Get device statistics"""
    try:
        total_devices = db.query(Device).filter(Device.is_active == True).count()

        online_devices = db.query(Device).filter(
            Device.is_active == True,
            Device.status == DeviceStatus.ONLINE
        ).count()

        offline_devices = db.query(Device).filter(
            Device.is_active == True,
            Device.status == DeviceStatus.OFFLINE
        ).count()

        warning_devices = db.query(Device).filter(
            Device.is_active == True,
            Device.status == DeviceStatus.WARNING
        ).count()

        critical_devices = db.query(Device).filter(
            Device.is_active == True,
            Device.status == DeviceStatus.CRITICAL
        ).count()

        # Get device types distribution
        devices = db.query(Device).filter(Device.is_active == True).all()
        device_types = {}
        total_health = 0

        for device in devices:
            device_type = device.device_type.value
            device_types[device_type] = device_types.get(device_type, 0) + 1
            total_health += device.health_score or 0

        average_health = total_health / total_devices if total_devices > 0 else 0

        return DeviceStats(
            total_devices=total_devices,
            online_devices=online_devices,
            offline_devices=offline_devices,
            warning_devices=warning_devices,
            critical_devices=critical_devices,
            device_types=device_types,
            average_health_score=round(average_health, 2)
        )

    except Exception as e:
        logger.error(f"Error getting device stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve device statistics")


@router.post("/{device_id}/test-connection")
async def test_device_connection(device_id: int, db: Session = Depends(get_db)):
    """Test SNMP connection to device"""
    try:
        device = db.query(Device).filter(
            Device.id == device_id,
            Device.is_active == True
        ).first()

        if not device:
            raise HTTPException(status_code=404, detail="Device not found")

        # Import here to avoid circular imports
        from ....services.snmp_service import SNMPCollector

        snmp_collector = SNMPCollector()
        is_connected = await snmp_collector.test_connectivity(
            device.ip_address,
            device.snmp_community
        )

        return {
            "device_id": device_id,
            "ip_address": device.ip_address,
            "connected": is_connected,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing device connection {device_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to test device connection")


@router.post("/{device_id}/collect-metrics")
async def collect_device_metrics(device_id: int, db: Session = Depends(get_db)):
    """Manually trigger metrics collection for device"""
    try:
        device = db.query(Device).filter(
            Device.id == device_id,
            Device.is_active == True
        ).first()

        if not device:
            raise HTTPException(status_code=404, detail="Device not found")

        # Import here to avoid circular imports
        from ....services.device_service import DeviceService

        device_service = DeviceService()
        result = await device_service.monitor_device(device, db)

        return {
            "message": "Metrics collection completed",
            "result": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error collecting metrics for device {device_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to collect device metrics")


@router.get("/{device_id}/health")
async def get_device_health(device_id: int, db: Session = Depends(get_db)):
    """Get device health summary"""
    try:
        # Import here to avoid circular imports
        from ....services.device_service import DeviceService

        device_service = DeviceService()
        health_summary = await device_service.get_device_health_summary(device_id)

        if 'error' in health_summary:
            raise HTTPException(status_code=404, detail=health_summary['error'])

        return health_summary

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting device health for device {device_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve device health")

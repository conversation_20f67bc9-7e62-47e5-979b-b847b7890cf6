"""
Network device models
"""
from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, String, Integer, Float, Text, JSON, Enum, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.mysql import LONGTEXT
import enum

from .base import BaseModel


class DeviceType(str, enum.Enum):
    """Device type enumeration"""
    SWITCH = "switch"
    ROUTER = "router"
    FIREWALL = "firewall"
    ACCESS_POINT = "access_point"
    SERVER = "server"
    WORKSTATION = "workstation"
    PRINTER = "printer"
    OTHER = "other"


class DeviceStatus(str, enum.Enum):
    """Device status enumeration"""
    ONLINE = "online"
    OFFLINE = "offline"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class Device(BaseModel):
    """Network device model"""
    __tablename__ = "devices"
    
    # Basic Information
    name = Column(String(255), nullable=False, index=True)
    ip_address = Column(String(45), nullable=False, unique=True, index=True)  # Support IPv6
    mac_address = Column(String(17), nullable=True, index=True)
    device_type = Column(Enum(DeviceType), nullable=False, default=DeviceType.OTHER)
    vendor = Column(String(100), nullable=True)
    model = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    
    # Location Information
    location = Column(String(255), nullable=True)
    building = Column(String(100), nullable=True)
    floor = Column(String(50), nullable=True)
    room = Column(String(50), nullable=True)
    
    # Network Configuration
    subnet = Column(String(50), nullable=True)
    vlan_id = Column(Integer, nullable=True)
    snmp_community = Column(String(100), nullable=True, default="public")
    snmp_version = Column(String(10), nullable=True, default="2c")
    snmp_port = Column(Integer, nullable=True, default=161)
    
    # Status and Monitoring
    status = Column(Enum(DeviceStatus), nullable=False, default=DeviceStatus.UNKNOWN)
    last_seen = Column(String(50), nullable=True)  # Store as ISO string
    uptime = Column(Integer, nullable=True)  # Seconds
    cpu_usage = Column(Float, nullable=True)  # Percentage
    memory_usage = Column(Float, nullable=True)  # Percentage
    disk_usage = Column(Float, nullable=True)  # Percentage
    
    # Additional Data
    system_info = Column(JSON, nullable=True)  # Store system information
    interfaces = Column(JSON, nullable=True)  # Store interface information
    custom_fields = Column(JSON, nullable=True)  # Store custom fields
    
    # Relationships
    alerts = relationship("Alert", back_populates="device", cascade="all, delete-orphan")
    performance_metrics = relationship("PerformanceMetric", back_populates="device", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Device(name='{self.name}', ip='{self.ip_address}', status='{self.status}')>"
    
    @property
    def is_online(self) -> bool:
        """Check if device is online"""
        return self.status == DeviceStatus.ONLINE
    
    @property
    def health_score(self) -> float:
        """Calculate device health score (0-100)"""
        if self.status == DeviceStatus.OFFLINE:
            return 0.0
        elif self.status == DeviceStatus.CRITICAL:
            return 25.0
        elif self.status == DeviceStatus.WARNING:
            return 50.0
        elif self.status == DeviceStatus.ONLINE:
            # Calculate based on resource usage
            cpu_score = 100 - (self.cpu_usage or 0)
            memory_score = 100 - (self.memory_usage or 0)
            disk_score = 100 - (self.disk_usage or 0)
            return (cpu_score + memory_score + disk_score) / 3
        else:
            return 75.0  # Unknown status

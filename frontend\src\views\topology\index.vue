<template>
  <div class="topology">
    <a-row :gutter="16" class="topology-container">
      <!-- Control Panel -->
      <a-col :xs="24" :lg="6" class="control-panel">
        <a-card title="拓扑控制" size="small">
          <!-- Layout Controls -->
          <div class="control-section">
            <h4>布局算法</h4>
            <a-select 
              v-model:value="layoutType" 
              @change="changeLayout"
              style="width: 100%"
            >
              <a-select-option value="force">力导向布局</a-select-option>
              <a-select-option value="circular">环形布局</a-select-option>
              <a-select-option value="grid">网格布局</a-select-option>
              <a-select-option value="dagre">层次布局</a-select-option>
              <a-select-option value="radial">径向布局</a-select-option>
            </a-select>
          </div>
          
          <!-- View Controls -->
          <div class="control-section">
            <h4>视图控制</h4>
            <a-space direction="vertical" style="width: 100%">
              <a-button block @click="fitView">
                <template #icon><ExpandOutlined /></template>
                适应视图
              </a-button>
              <a-button block @click="resetZoom">
                <template #icon><ReloadOutlined /></template>
                重置缩放
              </a-button>
              <a-button block @click="exportImage">
                <template #icon><DownloadOutlined /></template>
                导出图片
              </a-button>
            </a-space>
          </div>
          
          <!-- Filter Controls -->
          <div class="control-section">
            <h4>设备筛选</h4>
            <a-checkbox-group 
              v-model:value="deviceTypeFilter" 
              @change="filterDevices"
              style="width: 100%"
            >
              <a-row>
                <a-col :span="24" v-for="type in deviceTypes" :key="type">
                  <a-checkbox :value="type">{{ getDeviceTypeName(type) }}</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </div>
          
          <!-- Status Filter -->
          <div class="control-section">
            <h4>状态筛选</h4>
            <a-checkbox-group 
              v-model:value="statusFilter" 
              @change="filterDevices"
              style="width: 100%"
            >
              <a-row>
                <a-col :span="24" v-for="status in deviceStatuses" :key="status">
                  <a-checkbox :value="status">{{ getStatusName(status) }}</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </div>
          
          <!-- Legend -->
          <div class="control-section">
            <h4>图例</h4>
            <div class="legend">
              <div class="legend-item" v-for="status in deviceStatuses" :key="status">
                <div 
                  class="legend-color" 
                  :style="{ backgroundColor: getStatusColor(status) }"
                ></div>
                <span>{{ getStatusName(status) }}</span>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
      
      <!-- Topology Graph -->
      <a-col :xs="24" :lg="18" class="graph-container">
        <a-card class="graph-card">
          <template #title>
            <div class="graph-header">
              <span>网络拓扑图</span>
              <a-badge 
                :count="filteredDevices.length" 
                :number-style="{ backgroundColor: '#52c41a' }"
              >
                <span>设备数量</span>
              </a-badge>
            </div>
          </template>
          
          <template #extra>
            <a-space>
              <a-tooltip title="全屏">
                <a-button 
                  type="text" 
                  :icon="h(FullscreenOutlined)" 
                  @click="toggleFullscreen"
                />
              </a-tooltip>
              <a-tooltip title="刷新">
                <a-button 
                  type="text" 
                  :icon="h(ReloadOutlined)" 
                  @click="refreshTopology"
                  :loading="loading"
                />
              </a-tooltip>
            </a-space>
          </template>
          
          <!-- Graph Container -->
          <div 
            ref="graphContainer" 
            class="graph-canvas"
            :class="{ 'fullscreen': isFullscreen }"
          >
            <div v-if="loading" class="loading-overlay">
              <a-spin size="large" />
              <div>加载拓扑数据中...</div>
            </div>
          </div>
          
          <!-- Node Info Panel -->
          <div v-if="selectedNode" class="node-info-panel">
            <a-card size="small" :title="selectedNode.name">
              <template #extra>
                <a-button 
                  type="text" 
                  size="small"
                  :icon="h(CloseOutlined)" 
                  @click="selectedNode = null"
                />
              </template>
              
              <a-descriptions size="small" :column="1">
                <a-descriptions-item label="IP地址">
                  {{ selectedNode.ip_address }}
                </a-descriptions-item>
                <a-descriptions-item label="设备类型">
                  {{ getDeviceTypeName(selectedNode.device_type) }}
                </a-descriptions-item>
                <a-descriptions-item label="状态">
                  <a-badge 
                    :status="getDeviceStatusBadge(selectedNode.status)" 
                    :text="getStatusName(selectedNode.status)"
                  />
                </a-descriptions-item>
                <a-descriptions-item label="位置" v-if="selectedNode.location">
                  {{ selectedNode.location }}
                </a-descriptions-item>
                <a-descriptions-item label="CPU使用率" v-if="selectedNode.cpu_usage">
                  {{ selectedNode.cpu_usage }}%
                </a-descriptions-item>
                <a-descriptions-item label="内存使用率" v-if="selectedNode.memory_usage">
                  {{ selectedNode.memory_usage }}%
                </a-descriptions-item>
              </a-descriptions>
              
              <a-space style="margin-top: 12px">
                <a-button 
                  size="small" 
                  type="primary"
                  @click="$router.push(`/devices/${selectedNode.id}`)"
                >
                  查看详情
                </a-button>
                <a-button 
                  size="small"
                  @click="testDeviceConnection(selectedNode.id)"
                >
                  测试连接
                </a-button>
              </a-space>
            </a-card>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed, h } from 'vue'
import { Graph } from '@antv/g6'
import { message } from 'ant-design-vue'
import { useDeviceStore } from '@/stores/device'
import { performanceApi } from '@/api/performance'
import { deviceApi } from '@/api/device'
import {
  ExpandOutlined,
  ReloadOutlined,
  DownloadOutlined,
  FullscreenOutlined,
  CloseOutlined
} from '@ant-design/icons-vue'

// Stores
const deviceStore = useDeviceStore()

// Refs
const graphContainer = ref<HTMLElement>()
const loading = ref(false)
const isFullscreen = ref(false)
const selectedNode = ref<any>(null)

// Graph instance
let graph: Graph | null = null

// State
const layoutType = ref('force')
const deviceTypeFilter = ref<string[]>([])
const statusFilter = ref<string[]>([])

// Data
const topologyData = ref<any>({ nodes: [], edges: [] })

// Constants
const deviceTypes = ['switch', 'router', 'firewall', 'access_point', 'server', 'workstation', 'printer', 'other']
const deviceStatuses = ['online', 'offline', 'warning', 'critical', 'unknown']

// Computed
const filteredDevices = computed(() => {
  return deviceStore.devices.filter(device => {
    const typeMatch = deviceTypeFilter.value.length === 0 || deviceTypeFilter.value.includes(device.device_type)
    const statusMatch = statusFilter.value.length === 0 || statusFilter.value.includes(device.status)
    return typeMatch && statusMatch
  })
})

// Methods
const getDeviceTypeName = (type: string) => {
  const typeNames: Record<string, string> = {
    switch: '交换机',
    router: '路由器',
    firewall: '防火墙',
    access_point: '接入点',
    server: '服务器',
    workstation: '工作站',
    printer: '打印机',
    other: '其他'
  }
  return typeNames[type] || type
}

const getStatusName = (status: string) => {
  const statusNames: Record<string, string> = {
    online: '在线',
    offline: '离线',
    warning: '告警',
    critical: '严重',
    unknown: '未知'
  }
  return statusNames[status] || status
}

const getStatusColor = (status: string) => {
  const statusColors: Record<string, string> = {
    online: '#52c41a',
    offline: '#ff4d4f',
    warning: '#faad14',
    critical: '#ff4d4f',
    unknown: '#d9d9d9'
  }
  return statusColors[status] || '#d9d9d9'
}

const getDeviceStatusBadge = (status: string) => {
  const statusMap: Record<string, string> = {
    online: 'success',
    offline: 'error',
    warning: 'warning',
    critical: 'error',
    unknown: 'default'
  }
  return statusMap[status] || 'default'
}

const initGraph = async () => {
  if (!graphContainer.value) return

  try {
    loading.value = true
    
    // Fetch topology data
    await fetchTopologyData()
    
    // Create graph instance
    graph = new Graph({
      container: graphContainer.value,
      width: graphContainer.value.clientWidth,
      height: graphContainer.value.clientHeight,
      data: topologyData.value,
      node: {
        type: 'circle',
        style: {
          size: (d: any) => {
            const baseSize = 20
            const sizeMap: Record<string, number> = {
              switch: 30,
              router: 28,
              firewall: 26,
              server: 24,
              access_point: 22,
              workstation: 20,
              printer: 18,
              other: 16
            }
            return sizeMap[d.device_type] || baseSize
          },
          fill: (d: any) => getStatusColor(d.status),
          stroke: '#fff',
          lineWidth: 2,
          labelText: (d: any) => d.name,
          labelFill: '#000',
          labelFontSize: 12,
          labelPosition: 'bottom'
        }
      },
      edge: {
        type: 'line',
        style: {
          stroke: '#e2e2e2',
          lineWidth: 1,
          endArrow: true,
          endArrowType: 'triangle',
          endArrowSize: 6
        }
      },
      layout: {
        type: layoutType.value,
        preventOverlap: true,
        nodeSize: 30,
        linkDistance: 100
      },
      behaviors: [
        'drag-canvas',
        'zoom-canvas',
        'drag-element',
        'click-select'
      ],
      plugins: [
        {
          type: 'grid-line',
          size: 20,
          stroke: '#f0f0f0'
        }
      ]
    })

    // Add event listeners
    graph.on('node:click', (event: any) => {
      const nodeData = event.target.data
      selectedNode.value = nodeData
    })

    graph.on('canvas:click', () => {
      selectedNode.value = null
    })

    // Render graph
    graph.render()
    
    // Fit view
    setTimeout(() => {
      graph?.fitView()
    }, 100)

  } catch (error) {
    console.error('Failed to initialize graph:', error)
    message.error('初始化拓扑图失败')
  } finally {
    loading.value = false
  }
}

const fetchTopologyData = async () => {
  try {
    // Fetch devices and topology data
    await deviceStore.fetchDevices()
    const topologyResponse = await performanceApi.getTopology()
    
    // Convert devices to nodes
    const nodes = deviceStore.devices.map(device => ({
      id: device.id.toString(),
      name: device.name,
      ip_address: device.ip_address,
      device_type: device.device_type,
      status: device.status,
      location: device.location,
      cpu_usage: device.cpu_usage,
      memory_usage: device.memory_usage,
      ...device
    }))
    
    // Use topology edges or create sample connections
    let edges = []
    if (topologyResponse.data?.edges) {
      edges = topologyResponse.data.edges.map((edge: any) => ({
        id: `${edge.source}-${edge.target}`,
        source: edge.source,
        target: edge.target,
        ...edge
      }))
    } else {
      // Create sample connections for demo
      edges = createSampleConnections(nodes)
    }
    
    topologyData.value = { nodes, edges }
    
  } catch (error) {
    console.error('Failed to fetch topology data:', error)
    // Use sample data for demo
    topologyData.value = createSampleTopology()
  }
}

const createSampleConnections = (nodes: any[]) => {
  const edges = []
  const switches = nodes.filter(n => n.device_type === 'switch')
  const routers = nodes.filter(n => n.device_type === 'router')
  const others = nodes.filter(n => !['switch', 'router'].includes(n.device_type))
  
  // Connect routers to switches
  if (routers.length > 0 && switches.length > 0) {
    routers.forEach(router => {
      switches.forEach(sw => {
        edges.push({
          id: `${router.id}-${sw.id}`,
          source: router.id,
          target: sw.id
        })
      })
    })
  }
  
  // Connect other devices to switches
  if (switches.length > 0) {
    others.forEach((device, index) => {
      const switchIndex = index % switches.length
      edges.push({
        id: `${switches[switchIndex].id}-${device.id}`,
        source: switches[switchIndex].id,
        target: device.id
      })
    })
  }
  
  return edges
}

const createSampleTopology = () => {
  // Sample topology data for demo
  const nodes = [
    { id: '1', name: 'Core Router', device_type: 'router', status: 'online' },
    { id: '2', name: 'Switch 1', device_type: 'switch', status: 'online' },
    { id: '3', name: 'Switch 2', device_type: 'switch', status: 'warning' },
    { id: '4', name: 'Firewall', device_type: 'firewall', status: 'online' },
    { id: '5', name: 'Server 1', device_type: 'server', status: 'online' },
    { id: '6', name: 'Server 2', device_type: 'server', status: 'offline' },
    { id: '7', name: 'AP 1', device_type: 'access_point', status: 'online' },
    { id: '8', name: 'AP 2', device_type: 'access_point', status: 'online' }
  ]
  
  const edges = [
    { id: 'e1', source: '1', target: '2' },
    { id: 'e2', source: '1', target: '3' },
    { id: 'e3', source: '1', target: '4' },
    { id: 'e4', source: '2', target: '5' },
    { id: 'e5', source: '2', target: '7' },
    { id: 'e6', source: '3', target: '6' },
    { id: 'e7', source: '3', target: '8' }
  ]
  
  return { nodes, edges }
}

const changeLayout = (type: string) => {
  if (!graph) return
  
  graph.updateLayout({
    type,
    preventOverlap: true,
    nodeSize: 30,
    linkDistance: type === 'force' ? 100 : 80
  })
  
  graph.render()
}

const filterDevices = () => {
  if (!graph) return
  
  const filteredNodes = topologyData.value.nodes.filter((node: any) => {
    const typeMatch = deviceTypeFilter.value.length === 0 || deviceTypeFilter.value.includes(node.device_type)
    const statusMatch = statusFilter.value.length === 0 || statusFilter.value.includes(node.status)
    return typeMatch && statusMatch
  })
  
  const nodeIds = new Set(filteredNodes.map((n: any) => n.id))
  const filteredEdges = topologyData.value.edges.filter((edge: any) => 
    nodeIds.has(edge.source) && nodeIds.has(edge.target)
  )
  
  graph.updateData({ nodes: filteredNodes, edges: filteredEdges })
  graph.render()
}

const fitView = () => {
  graph?.fitView()
}

const resetZoom = () => {
  graph?.zoomTo(1)
  graph?.fitCenter()
}

const exportImage = () => {
  if (!graph) return
  
  const canvas = graph.getCanvas()
  const dataURL = canvas.getDataURL()
  
  const link = document.createElement('a')
  link.download = `topology-${new Date().getTime()}.png`
  link.href = dataURL
  link.click()
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  
  nextTick(() => {
    if (graph && graphContainer.value) {
      graph.resize(graphContainer.value.clientWidth, graphContainer.value.clientHeight)
      graph.fitView()
    }
  })
}

const refreshTopology = async () => {
  await fetchTopologyData()
  if (graph) {
    graph.updateData(topologyData.value)
    graph.render()
    graph.fitView()
  }
}

const testDeviceConnection = async (deviceId: number) => {
  try {
    const result = await deviceApi.testConnection(deviceId)
    if (result.connected) {
      message.success('设备连接正常')
    } else {
      message.error('设备连接失败')
    }
  } catch (error) {
    message.error('测试连接失败')
  }
}

// Handle window resize
const handleResize = () => {
  if (graph && graphContainer.value) {
    graph.resize(graphContainer.value.clientWidth, graphContainer.value.clientHeight)
  }
}

// Lifecycle
onMounted(async () => {
  // Initialize filters
  deviceTypeFilter.value = [...deviceTypes]
  statusFilter.value = [...deviceStatuses]
  
  await nextTick()
  await initGraph()
  
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (graph) {
    graph.destroy()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.topology {
  padding: 24px;
  background: var(--bg-color);
  min-height: 100vh;
}

.topology-container {
  height: calc(100vh - 48px);
}

.control-panel {
  height: 100%;
  overflow-y: auto;
}

.control-section {
  margin-bottom: 20px;
}

.control-section h4 {
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
}

.legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.graph-container {
  height: 100%;
}

.graph-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.graph-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.graph-canvas {
  position: relative;
  height: calc(100vh - 200px);
  background: #fafafa;
  border: 1px solid var(--border-color-light);
  border-radius: 6px;
}

.graph-canvas.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999;
  background: white;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.node-info-panel {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 300px;
  z-index: 100;
  box-shadow: var(--box-shadow);
}

/* Responsive */
@media (max-width: 768px) {
  .topology {
    padding: 16px;
  }
  
  .topology-container {
    height: auto;
  }
  
  .control-panel {
    margin-bottom: 16px;
  }
  
  .graph-canvas {
    height: 400px;
  }
  
  .node-info-panel {
    position: relative;
    width: 100%;
    margin-top: 16px;
  }
}
</style>

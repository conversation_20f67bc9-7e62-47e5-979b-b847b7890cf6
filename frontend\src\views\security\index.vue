<template>
  <div class="security-overview">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>网络安全中心</h1>
      <a-button type="primary" @click="refreshAllData" :loading="loading">
        <template #icon><ReloadOutlined /></template>
        刷新数据
      </a-button>
    </div>

    <!-- 安全状态概览 -->
    <a-row :gutter="16" class="security-stats">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="安全状态"
            :value="securityStatus.level"
            :value-style="{ color: getSecurityStatusColor() }"
          >
            <template #prefix>
              <component :is="getSecurityStatusIcon()" />
            </template>
          </a-statistic>
          <div class="security-score">
            安全评分: {{ securityStatus.score }}/100
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="活跃威胁"
            :value="threatStats.high_severity_threats"
            :value-style="{ color: '#ff4d4f' }"
          >
            <template #prefix><ExclamationCircleOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="黑名单IP"
            :value="threatStats.blacklist_entries"
            :value-style="{ color: '#fa8c16' }"
          >
            <template #prefix><StopOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="威胁情报"
            :value="threatStats.total_threats"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix><SecurityScanOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 功能快捷入口 -->
    <a-card title="安全管理功能" class="function-card">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-card 
            size="small" 
            hoverable 
            class="function-item"
            @click="$router.push('/security/threat-intel')"
          >
            <template #cover>
              <div class="function-icon">
                <SecurityScanOutlined style="font-size: 48px; color: #1890ff;" />
              </div>
            </template>
            <a-card-meta 
              title="威胁情报仪表盘" 
              description="查看威胁统计、分析威胁趋势、管理威胁情报数据"
            />
            <div class="function-stats">
              <a-tag color="blue">{{ threatStats.total_threats }} 条情报</a-tag>
              <a-tag color="red">{{ threatStats.high_severity_threats }} 高危</a-tag>
            </div>
          </a-card>
        </a-col>
        
        <a-col :span="8">
          <a-card 
            size="small" 
            hoverable 
            class="function-item"
            @click="$router.push('/security/ip-reputation')"
          >
            <template #cover>
              <div class="function-icon">
                <SearchOutlined style="font-size: 48px; color: #52c41a;" />
              </div>
            </template>
            <a-card-meta 
              title="IP信誉查询" 
              description="检查IP地址安全状态、批量查询、威胁分析"
            />
            <div class="function-stats">
              <a-tag color="green">单个查询</a-tag>
              <a-tag color="orange">批量检测</a-tag>
            </div>
          </a-card>
        </a-col>
        
        <a-col :span="8">
          <a-card 
            size="small" 
            hoverable 
            class="function-item"
            @click="$router.push('/security/blacklist')"
          >
            <template #cover>
              <div class="function-icon">
                <StopOutlined style="font-size: 48px; color: #ff4d4f;" />
              </div>
            </template>
            <a-card-meta 
              title="黑名单管理" 
              description="管理威胁IP黑名单、添加/移除IP、批量操作"
            />
            <div class="function-stats">
              <a-tag color="red">{{ threatStats.blacklist_entries }} 个IP</a-tag>
              <a-tag color="purple">{{ threatStats.builtin_blacklist_count }} 内置</a-tag>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-card>

    <!-- 最新安全事件 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-card title="最新威胁事件" :loading="eventsLoading">
          <template #extra>
            <a-button size="small" @click="loadRecentEvents">刷新</a-button>
          </template>
          
          <a-timeline>
            <a-timeline-item
              v-for="event in recentEvents"
              :key="event.id"
              :color="getEventColor(event.threat_level)"
            >
              <template #dot>
                <component :is="getEventIcon(event.threat_level)" />
              </template>
              <div class="event-item">
                <div class="event-header">
                  <span class="event-ip">{{ event.ip_address }}</span>
                  <a-tag :color="getThreatLevelColor(event.threat_level)" size="small">
                    {{ getThreatLevelLabel(event.threat_level) }}
                  </a-tag>
                </div>
                <div class="event-reason">{{ event.reason }}</div>
                <div class="event-time">{{ formatTime(event.last_seen) }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
          
          <div v-if="recentEvents.length === 0" class="empty-events">
            <a-empty description="暂无威胁事件" />
          </div>
        </a-card>
      </a-col>
      
      <a-col :span="12">
        <a-card title="安全建议" :loading="recommendationsLoading">
          <template #extra>
            <a-button size="small" @click="generateRecommendations">生成建议</a-button>
          </template>
          
          <a-list
            :data-source="securityRecommendations"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <template #actions>
                  <a-button size="small" type="link" @click="executeRecommendation(item)">
                    执行
                  </a-button>
                </template>
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: item.color }">
                      <component :is="item.icon" />
                    </a-avatar>
                  </template>
                  <template #title>{{ item.title }}</template>
                  <template #description>{{ item.description }}</template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
          
          <div v-if="securityRecommendations.length === 0" class="empty-recommendations">
            <a-empty description="暂无安全建议" />
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  ExclamationCircleOutlined,
  StopOutlined,
  SecurityScanOutlined,
  SearchOutlined,
  SafetyCertificateOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'
import { threatApi, threatUtils, type ThreatStatistics, type ThreatIntelligenceItem } from '@/api/threat'

// 响应式数据
const loading = ref(false)
const eventsLoading = ref(false)
const recommendationsLoading = ref(false)

const threatStats = ref<ThreatStatistics>({
  total_threats: 0,
  high_severity_threats: 0,
  blacklist_entries: 0,
  builtin_blacklist_count: 0
})

const securityStatus = reactive({
  level: '良好',
  score: 85
})

const recentEvents = ref<ThreatIntelligenceItem[]>([])

const securityRecommendations = ref([
  {
    id: 1,
    title: '更新威胁情报数据',
    description: '建议定期更新威胁情报数据以获取最新的安全信息',
    icon: 'SecurityScanOutlined',
    color: '#1890ff',
    action: 'update_threat_intel'
  },
  {
    id: 2,
    title: '检查高危IP',
    description: '发现高危威胁IP，建议立即加入黑名单',
    icon: 'ExclamationCircleOutlined',
    color: '#ff4d4f',
    action: 'check_high_risk_ips'
  },
  {
    id: 3,
    title: '优化黑名单规则',
    description: '当前黑名单可能包含过期条目，建议清理',
    icon: 'StopOutlined',
    color: '#fa8c16',
    action: 'optimize_blacklist'
  }
])

// 工具函数
const getSecurityStatusColor = () => {
  if (securityStatus.score >= 80) return '#52c41a'
  if (securityStatus.score >= 60) return '#faad14'
  return '#ff4d4f'
}

const getSecurityStatusIcon = () => {
  if (securityStatus.score >= 80) return CheckCircleOutlined
  if (securityStatus.score >= 60) return WarningOutlined
  return CloseCircleOutlined
}

const getEventColor = (level: string) => {
  const colors = {
    critical: 'red',
    high: 'orange',
    medium: 'blue',
    low: 'green',
    safe: 'gray'
  }
  return colors[level as keyof typeof colors] || 'gray'
}

const getEventIcon = (level: string) => {
  const icons = {
    critical: CloseCircleOutlined,
    high: ExclamationCircleOutlined,
    medium: WarningOutlined,
    low: InfoCircleOutlined,
    safe: CheckCircleOutlined
  }
  return icons[level as keyof typeof icons] || InfoCircleOutlined
}

const getThreatLevelColor = (level: string) => {
  const info = threatUtils.getThreatLevelInfo(level)
  return info.color
}

const getThreatLevelLabel = (level: string) => {
  const info = threatUtils.getThreatLevelInfo(level)
  return info.label
}

const formatTime = (timestamp?: string) => {
  return threatUtils.formatTimestamp(timestamp)
}

// 数据加载函数
const loadThreatStats = async () => {
  try {
    const response = await threatApi.getThreatStatistics()
    threatStats.value = response.data
    
    // 根据威胁统计更新安全状态
    updateSecurityStatus()
  } catch (error) {
    console.error('加载威胁统计失败:', error)
    message.error('加载威胁统计失败')
  }
}

const loadRecentEvents = async () => {
  try {
    eventsLoading.value = true
    const response = await threatApi.getThreatIntelligence({ limit: 10 })
    recentEvents.value = response.data.items.filter(item => item.is_malicious)
  } catch (error) {
    console.error('加载最新事件失败:', error)
    message.error('加载最新事件失败')
  } finally {
    eventsLoading.value = false
  }
}

const updateSecurityStatus = () => {
  const { total_threats, high_severity_threats, blacklist_entries } = threatStats.value
  
  // 简单的安全评分算法
  let score = 100
  
  // 高危威胁扣分
  if (high_severity_threats > 0) {
    score -= Math.min(high_severity_threats * 10, 40)
  }
  
  // 威胁情报数量影响
  if (total_threats > 100) {
    score -= 10
  }
  
  // 黑名单数量影响
  if (blacklist_entries > 50) {
    score -= 5
  }
  
  securityStatus.score = Math.max(score, 0)
  
  if (score >= 80) {
    securityStatus.level = '良好'
  } else if (score >= 60) {
    securityStatus.level = '一般'
  } else {
    securityStatus.level = '警告'
  }
}

const generateRecommendations = async () => {
  try {
    recommendationsLoading.value = true
    
    // 根据当前威胁状态生成建议
    const recommendations = []
    
    if (threatStats.value.high_severity_threats > 0) {
      recommendations.push({
        id: Date.now() + 1,
        title: '处理高危威胁',
        description: `发现${threatStats.value.high_severity_threats}个高危威胁，建议立即处理`,
        icon: 'ExclamationCircleOutlined',
        color: '#ff4d4f',
        action: 'handle_high_threats'
      })
    }
    
    if (threatStats.value.total_threats > 100) {
      recommendations.push({
        id: Date.now() + 2,
        title: '清理威胁情报',
        description: '威胁情报数据较多，建议清理过期数据',
        icon: 'SecurityScanOutlined',
        color: '#1890ff',
        action: 'cleanup_threats'
      })
    }
    
    if (recommendations.length > 0) {
      securityRecommendations.value = recommendations
    }
    
    message.success('安全建议已更新')
  } catch (error) {
    console.error('生成安全建议失败:', error)
    message.error('生成安全建议失败')
  } finally {
    recommendationsLoading.value = false
  }
}

const executeRecommendation = async (recommendation: any) => {
  switch (recommendation.action) {
    case 'update_threat_intel':
      try {
        await threatApi.updateThreatIntelligence()
        message.success('威胁情报更新成功')
        await refreshAllData()
      } catch (error) {
        message.error('威胁情报更新失败')
      }
      break
    case 'check_high_risk_ips':
      message.info('正在跳转到IP信誉查询页面...')
      setTimeout(() => {
        window.open('/security/ip-reputation', '_blank')
      }, 1000)
      break
    case 'optimize_blacklist':
      message.info('正在跳转到黑名单管理页面...')
      setTimeout(() => {
        window.open('/security/blacklist', '_blank')
      }, 1000)
      break
    default:
      message.info('功能开发中...')
  }
}

const refreshAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadThreatStats(),
      loadRecentEvents()
    ])
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载
onMounted(() => {
  refreshAllData()
})
</script>

<style scoped>
.security-overview {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.security-stats {
  margin-bottom: 24px;
}

.security-score {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.function-card {
  margin-bottom: 24px;
}

.function-item {
  height: 100%;
  cursor: pointer;
  transition: all 0.3s;
}

.function-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.function-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.function-stats {
  margin-top: 12px;
  text-align: center;
}

.function-stats .ant-tag {
  margin: 2px;
}

.event-item {
  padding: 8px 0;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.event-ip {
  font-weight: 600;
  font-family: monospace;
}

.event-reason {
  color: #666;
  font-size: 12px;
  margin-bottom: 4px;
}

.event-time {
  color: #999;
  font-size: 11px;
}

.empty-events,
.empty-recommendations {
  text-align: center;
  padding: 20px;
}
</style>

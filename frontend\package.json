{"name": "campusguard-frontend", "version": "1.0.0", "description": "Campus<PERSON>uard Frontend Application", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.5.13", "vue-router": "^4.5.0", "pinia": "^2.3.0", "ant-design-vue": "^4.2.6", "ant-design-x-vue": "^1.2.7", "@antv/g6": "^5.0.29", "axios": "^1.7.9", "dayjs": "^1.11.13", "echarts": "^5.5.1", "vue-echarts": "^7.0.3"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "vite": "^6.0.3", "eslint": "^9.15.0", "eslint-plugin-vue": "^9.31.0", "prettier": "^3.4.2", "@types/node": "^22.10.2", "typescript": "^5.7.2", "vue-tsc": "^2.1.10"}}
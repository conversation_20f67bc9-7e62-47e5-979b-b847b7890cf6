# CampusGuard项目建议的Shell命令

## 项目初始化
```bash
# 创建项目目录
mkdir campusguard && cd campusguard

# 初始化Git仓库
git init

# 创建Python虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 创建前端项目
npm create vue@latest frontend
cd frontend && npm install && cd ..

# 创建后端目录结构
mkdir -p backend/{app,config,models,api,services,utils}
```

## 开发环境启动
```bash
# 启动后端开发服务器
cd backend
source ../venv/bin/activate
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动前端开发服务器
cd frontend
npm run dev

# 启动MySQL数据库
sudo systemctl start mysql  # Linux
# 或 brew services start mysql  # Mac
# 或使用Docker: docker run -d --name mysql -e MYSQL_ROOT_PASSWORD=password -p 3306:3306 mysql:8.0
```

## 数据库操作
```bash
# 连接MySQL数据库
mysql -u root -p

# 创建数据库和用户
CREATE DATABASE campusguard;
CREATE USER 'campusguard'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON campusguard.* TO 'campusguard'@'localhost';
FLUSH PRIVILEGES;

# 运行数据库迁移
cd backend
python -m alembic upgrade head
```

## 测试和部署
```bash
# 运行后端测试
cd backend
python -m pytest tests/

# 运行前端测试
cd frontend
npm run test

# 构建前端生产版本
npm run build

# 启动生产环境
cd backend
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```
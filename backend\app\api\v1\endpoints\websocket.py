"""
WebSocket endpoints for real-time communication
"""
import json
import async<PERSON>
from typing import Dict, Set
from datetime import datetime
from fastapi import <PERSON><PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.orm import Session
from loguru import logger

from ....core.database import get_db
from ....models.device import Device
from ....models.alert import Alert

router = APIRouter()

# Connection manager for WebSocket connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()
        self.user_connections: Dict[str, Set[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str = None):
        """Accept WebSocket connection"""
        await websocket.accept()
        self.active_connections.add(websocket)
        
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = set()
            self.user_connections[user_id].add(websocket)
        
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket, user_id: str = None):
        """Remove WebSocket connection"""
        self.active_connections.discard(websocket)
        
        if user_id and user_id in self.user_connections:
            self.user_connections[user_id].discard(websocket)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send message to specific WebSocket"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
    
    async def send_to_user(self, message: str, user_id: str):
        """Send message to all connections of a specific user"""
        if user_id in self.user_connections:
            disconnected = set()
            for websocket in self.user_connections[user_id]:
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    logger.error(f"Error sending message to user {user_id}: {e}")
                    disconnected.add(websocket)
            
            # Remove disconnected websockets
            for websocket in disconnected:
                self.disconnect(websocket, user_id)
    
    async def broadcast(self, message: str):
        """Broadcast message to all connected clients"""
        disconnected = set()
        for websocket in self.active_connections:
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                disconnected.add(websocket)
        
        # Remove disconnected websockets
        for websocket in disconnected:
            self.disconnect(websocket)

# Global connection manager instance
manager = ConnectionManager()


@router.websocket("/connect")
async def websocket_endpoint(websocket: WebSocket, user_id: str = None):
    """Main WebSocket endpoint"""
    await manager.connect(websocket, user_id)
    
    try:
        # Send welcome message
        welcome_message = {
            "type": "connection",
            "status": "connected",
            "timestamp": datetime.now().isoformat(),
            "message": "Connected to CampusGuard real-time updates"
        }
        await manager.send_personal_message(json.dumps(welcome_message), websocket)
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # Handle different message types
                if message_data.get("type") == "ping":
                    # Respond to ping with pong
                    pong_message = {
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(pong_message), websocket)
                
                elif message_data.get("type") == "subscribe":
                    # Handle subscription to specific data types
                    subscription_type = message_data.get("subscription")
                    response = {
                        "type": "subscription",
                        "status": "subscribed",
                        "subscription": subscription_type,
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(response), websocket)
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                # Invalid JSON received
                error_message = {
                    "type": "error",
                    "message": "Invalid JSON format",
                    "timestamp": datetime.now().isoformat()
                }
                await manager.send_personal_message(json.dumps(error_message), websocket)
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                error_message = {
                    "type": "error",
                    "message": "Internal server error",
                    "timestamp": datetime.now().isoformat()
                }
                await manager.send_personal_message(json.dumps(error_message), websocket)
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        manager.disconnect(websocket, user_id)


# Utility functions for sending real-time updates
async def broadcast_device_update(device_data: dict):
    """Broadcast device status update to all connected clients"""
    message = {
        "type": "device_update",
        "data": device_data,
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast(json.dumps(message))


async def broadcast_alert(alert_data: dict):
    """Broadcast new alert to all connected clients"""
    message = {
        "type": "new_alert",
        "data": alert_data,
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast(json.dumps(message))


async def broadcast_performance_update(performance_data: dict):
    """Broadcast performance metrics update"""
    message = {
        "type": "performance_update",
        "data": performance_data,
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast(json.dumps(message))


async def send_ai_response(user_id: str, response_data: dict):
    """Send AI response to specific user"""
    message = {
        "type": "ai_response",
        "data": response_data,
        "timestamp": datetime.now().isoformat()
    }
    await manager.send_to_user(json.dumps(message), user_id)


# Health check endpoint for WebSocket
@router.get("/health")
async def websocket_health():
    """WebSocket service health check"""
    return {
        "status": "healthy",
        "active_connections": len(manager.active_connections),
        "user_connections": len(manager.user_connections),
        "timestamp": datetime.now().isoformat()
    }

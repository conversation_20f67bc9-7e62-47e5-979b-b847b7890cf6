"""
Threat Intelligence Pydantic Schemas
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

from ..models.threat import ThreatLevel, ThreatSource


class IPReputationRequest(BaseModel):
    """IP信誉检查请求"""
    ip_address: str = Field(..., description="要检查的IP地址")


class IPReputationResponse(BaseModel):
    """IP信誉检查响应"""
    ip_address: str
    is_malicious: bool
    threat_level: str
    reason: Optional[str] = None
    categories: Optional[List[str]] = None
    source: str
    confidence: float
    last_seen: Optional[str] = None
    error: Optional[str] = None


class BlacklistRequest(BaseModel):
    """黑名单添加请求"""
    ip_address: str = Field(..., description="IP地址")
    reason: str = Field(..., description="加入黑名单的原因")
    severity: str = Field(default="medium", description="严重程度")


class BlacklistResponse(BaseModel):
    """黑名单响应"""
    ip_address: str
    reason: str
    severity: str
    source: str
    added_by: Optional[str] = None
    added_at: str


class ThreatIntelligenceResponse(BaseModel):
    """威胁情报响应"""
    id: int
    ip_address: str
    domain: Optional[str] = None
    is_malicious: bool
    threat_level: ThreatLevel
    categories: List[str] = []
    description: Optional[str] = None
    source: ThreatSource
    confidence_score: float
    first_seen: Optional[datetime] = None
    last_seen: Optional[datetime] = None
    created_at: datetime

    class Config:
        from_attributes = True


class ThreatStatisticsResponse(BaseModel):
    """威胁统计响应"""
    total_threats: int
    high_severity_threats: int
    blacklist_entries: int
    builtin_blacklist_count: int
    last_update: Optional[str] = None
    error: Optional[str] = None


class ThreatEventRequest(BaseModel):
    """威胁事件创建请求"""
    event_type: str = Field(..., description="事件类型")
    event_name: str = Field(..., description="事件名称")
    description: Optional[str] = None
    threat_level: ThreatLevel = ThreatLevel.LOW
    source_ip: Optional[str] = None
    destination_ip: Optional[str] = None
    source_port: Optional[int] = None
    destination_port: Optional[int] = None
    protocol: Optional[str] = None
    device_id: Optional[int] = None


class ThreatEventResponse(BaseModel):
    """威胁事件响应"""
    id: int
    event_type: str
    event_name: str
    description: Optional[str] = None
    threat_level: ThreatLevel
    confidence: float
    source_ip: Optional[str] = None
    destination_ip: Optional[str] = None
    source_port: Optional[int] = None
    destination_port: Optional[int] = None
    protocol: Optional[str] = None
    device_id: Optional[int] = None
    status: str
    detected_at: datetime
    created_at: datetime

    class Config:
        from_attributes = True


class IOCRequest(BaseModel):
    """IOC创建请求"""
    ioc_type: str = Field(..., description="IOC类型")
    ioc_value: str = Field(..., description="IOC值")
    threat_type: Optional[str] = None
    malware_family: Optional[str] = None
    source: Optional[str] = None
    confidence: float = Field(default=0.5, ge=0.0, le=1.0)


class IOCResponse(BaseModel):
    """IOC响应"""
    id: int
    ioc_type: str
    ioc_value: str
    threat_type: Optional[str] = None
    malware_family: Optional[str] = None
    source: Optional[str] = None
    confidence: float
    first_seen: Optional[datetime] = None
    last_seen: Optional[datetime] = None
    created_at: datetime

    class Config:
        from_attributes = True

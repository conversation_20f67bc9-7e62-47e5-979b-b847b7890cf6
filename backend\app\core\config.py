"""
Application configuration settings
"""
import os
from typing import List, Optional, Union
from pydantic_settings import BaseSettings
from pydantic import field_validator
from loguru import logger


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    PROJECT_NAME: str = "CampusGuard"
    API_V1_STR: str = "/api/v1"
    DEBUG: bool = False
    
    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    WORKERS: int = 1
    
    # Security
    SECRET_KEY: str = "campusguard-secret-key-2024"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 10080  # 7 days
    ALGORITHM: str = "HS256"
    
    # Database
    DATABASE_URL: str = "mysql+pymysql://campusguard:campusguard123@localhost:3306/campusguard"
    DATABASE_HOST: str = "localhost"
    DATABASE_PORT: int = 3306
    DATABASE_NAME: str = "campusguard"
    DATABASE_USER: str = "campusguard"
    DATABASE_PASSWORD: str = "campusguard123"
    
    # DeepSeek API
    DEEPSEEK_API_KEY: str = ""
    DEEPSEEK_BASE_URL: str = "https://api.deepseek.com"
    DEEPSEEK_MODEL: str = "deepseek-chat"
    
    # CORS
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173", 
        "http://localhost:8080"
    ]
    
    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Network Monitoring
    SNMP_COMMUNITY: str = "public"
    SNMP_TIMEOUT: int = 5
    SNMP_RETRIES: int = 3
    SNMP_VERSION: str = "2c"
    
    # WebSocket
    WS_HOST: str = "0.0.0.0"
    WS_PORT: int = 8001
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE_PATH: str = "logs/campusguard.log"
    LOG_ROTATION: str = "1 week"
    LOG_RETENTION: str = "30 days"
    LOG_COMPRESSION: str = "zip"
    
    class Config:
        env_file = "config/.env"
        case_sensitive = True


# Global settings instance
settings = Settings()

# Note: Advanced logging configuration is handled in logging_config.py

"""
Threat Intelligence Service
"""
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import httpx
from loguru import logger

from ..core.config import settings
from ..core.database import SessionLocal
from ..models.threat import ThreatIntelligence, IPBlacklist


class ThreatIntelligenceService:
    """威胁情报服务"""
    
    def __init__(self):
        self.blacklist_cache = {}
        self.cache_ttl = 3600  # 1小时缓存
        self.last_update = None
        
        # 内置恶意IP黑名单
        self.builtin_blacklist = {
            "*************": {"reason": "恶意扫描", "severity": "high"},
            "*********": {"reason": "暴力破解", "severity": "critical"},
            "************": {"reason": "可疑连接", "severity": "medium"}
        }
    
    async def check_ip_reputation(self, ip_address: str) -> Dict[str, Any]:
        """检查IP地址信誉"""
        try:
            # 检查内置黑名单
            if ip_address in self.builtin_blacklist:
                threat_info = self.builtin_blacklist[ip_address]
                return {
                    "ip_address": ip_address,
                    "is_malicious": True,
                    "threat_level": threat_info["severity"],
                    "reason": threat_info["reason"],
                    "source": "builtin_blacklist",
                    "last_seen": datetime.now().isoformat(),
                    "confidence": 0.9
                }
            
            # 检查数据库缓存
            with SessionLocal() as db:
                threat_record = db.query(ThreatIntelligence).filter(
                    ThreatIntelligence.ip_address == ip_address
                ).first()
                
                if threat_record:
                    return {
                        "ip_address": ip_address,
                        "is_malicious": threat_record.is_malicious,
                        "threat_level": threat_record.threat_level,
                        "categories": threat_record.categories.split(",") if threat_record.categories else [],
                        "source": threat_record.source,
                        "last_seen": threat_record.last_seen.isoformat() if threat_record.last_seen else None,
                        "confidence": threat_record.confidence_score
                    }
            
            # 默认返回安全状态
            return {
                "ip_address": ip_address,
                "is_malicious": False,
                "threat_level": "safe",
                "reason": "未发现威胁",
                "source": "local_check",
                "confidence": 0.7
            }
            
        except Exception as e:
            logger.error(f"检查IP信誉失败: {e}")
            return {
                "ip_address": ip_address,
                "is_malicious": False,
                "threat_level": "unknown",
                "error": str(e)
            }
    
    async def batch_check_ips(self, ip_addresses: List[str]) -> Dict[str, Dict[str, Any]]:
        """批量检查IP地址"""
        results = {}
        
        tasks = [self.check_ip_reputation(ip) for ip in ip_addresses]
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        for ip, response in zip(ip_addresses, responses):
            if isinstance(response, Exception):
                results[ip] = {
                    "ip_address": ip,
                    "is_malicious": False,
                    "error": str(response)
                }
            else:
                results[ip] = response
        
        return results
    
    async def add_to_blacklist(self, ip_address: str, reason: str, severity: str = "medium") -> bool:
        """添加IP到黑名单"""
        try:
            with SessionLocal() as db:
                # 检查是否已存在
                existing = db.query(IPBlacklist).filter(
                    IPBlacklist.ip_address == ip_address
                ).first()
                
                if existing:
                    # 更新现有记录
                    existing.reason = reason
                    existing.severity = severity
                    existing.updated_at = datetime.now()
                else:
                    # 创建新记录
                    blacklist_entry = IPBlacklist(
                        ip_address=ip_address,
                        reason=reason,
                        severity=severity,
                        added_by="system",
                        created_at=datetime.now()
                    )
                    db.add(blacklist_entry)
                
                db.commit()
                
                # 更新内置黑名单缓存
                self.builtin_blacklist[ip_address] = {
                    "reason": reason,
                    "severity": severity
                }
                
                logger.info(f"IP {ip_address} 已添加到黑名单: {reason}")
                return True
                
        except Exception as e:
            logger.error(f"添加IP到黑名单失败: {e}")
            return False
    
    async def remove_from_blacklist(self, ip_address: str) -> bool:
        """从黑名单移除IP"""
        try:
            with SessionLocal() as db:
                blacklist_entry = db.query(IPBlacklist).filter(
                    IPBlacklist.ip_address == ip_address
                ).first()
                
                if blacklist_entry:
                    db.delete(blacklist_entry)
                    db.commit()
                    
                    # 从缓存中移除
                    if ip_address in self.builtin_blacklist:
                        del self.builtin_blacklist[ip_address]
                    
                    logger.info(f"IP {ip_address} 已从黑名单移除")
                    return True
                else:
                    logger.warning(f"IP {ip_address} 不在黑名单中")
                    return False
                    
        except Exception as e:
            logger.error(f"从黑名单移除IP失败: {e}")
            return False
    
    async def get_blacklist(self) -> List[Dict[str, Any]]:
        """获取黑名单列表"""
        try:
            blacklist = []
            
            # 添加内置黑名单
            for ip, info in self.builtin_blacklist.items():
                blacklist.append({
                    "ip_address": ip,
                    "reason": info["reason"],
                    "severity": info["severity"],
                    "source": "builtin",
                    "added_at": datetime.now().isoformat()
                })
            
            # 添加数据库中的黑名单
            with SessionLocal() as db:
                db_blacklist = db.query(IPBlacklist).filter(
                    IPBlacklist.is_active == True
                ).all()
                
                for entry in db_blacklist:
                    blacklist.append({
                        "ip_address": entry.ip_address,
                        "reason": entry.reason,
                        "severity": entry.severity,
                        "source": "database",
                        "added_by": entry.added_by,
                        "added_at": entry.created_at.isoformat()
                    })
            
            return blacklist
            
        except Exception as e:
            logger.error(f"获取黑名单失败: {e}")
            return []
    
    async def update_threat_intelligence(self) -> bool:
        """更新威胁情报数据"""
        try:
            logger.info("开始更新威胁情报数据")
            
            # 这里可以集成外部威胁情报源
            # 目前使用模拟数据
            sample_threats = [
                {
                    "ip_address": "*************",
                    "threat_level": "high",
                    "categories": "malware,botnet",
                    "source": "threat_feed",
                    "confidence_score": 0.85
                },
                {
                    "ip_address": "*************",
                    "threat_level": "medium", 
                    "categories": "scanning,reconnaissance",
                    "source": "threat_feed",
                    "confidence_score": 0.75
                }
            ]
            
            with SessionLocal() as db:
                for threat in sample_threats:
                    existing = db.query(ThreatIntelligence).filter(
                        ThreatIntelligence.ip_address == threat["ip_address"]
                    ).first()
                    
                    if existing:
                        # 更新现有记录
                        existing.threat_level = threat["threat_level"]
                        existing.categories = threat["categories"]
                        existing.confidence_score = threat["confidence_score"]
                        existing.updated_at = datetime.now()
                    else:
                        # 创建新记录
                        threat_record = ThreatIntelligence(
                            ip_address=threat["ip_address"],
                            is_malicious=True,
                            threat_level=threat["threat_level"],
                            categories=threat["categories"],
                            source=threat["source"],
                            confidence_score=threat["confidence_score"],
                            last_seen=datetime.now(),
                            created_at=datetime.now()
                        )
                        db.add(threat_record)
                
                db.commit()
            
            self.last_update = datetime.now()
            logger.info("威胁情报数据更新完成")
            return True
            
        except Exception as e:
            logger.error(f"更新威胁情报失败: {e}")
            return False
    
    async def get_threat_statistics(self) -> Dict[str, Any]:
        """获取威胁统计信息"""
        try:
            with SessionLocal() as db:
                # 威胁情报统计
                total_threats = db.query(ThreatIntelligence).count()
                high_threats = db.query(ThreatIntelligence).filter(
                    ThreatIntelligence.threat_level == "high"
                ).count()
                
                # 黑名单统计
                blacklist_count = db.query(IPBlacklist).filter(
                    IPBlacklist.is_active == True
                ).count()
                
                return {
                    "total_threats": total_threats,
                    "high_severity_threats": high_threats,
                    "blacklist_entries": blacklist_count,
                    "last_update": self.last_update.isoformat() if self.last_update else None,
                    "builtin_blacklist_count": len(self.builtin_blacklist)
                }
                
        except Exception as e:
            logger.error(f"获取威胁统计失败: {e}")
            return {
                "total_threats": 0,
                "high_severity_threats": 0,
                "blacklist_entries": 0,
                "error": str(e)
            }


# 全局威胁情报服务实例
threat_intelligence_service = ThreatIntelligenceService()

"""
Advanced logging configuration with <PERSON><PERSON><PERSON>
"""
import sys
import time
from pathlib import Path
from typing import Dict, Any
from loguru import logger
from datetime import datetime

from .config import settings


class LoggingConfig:
    """Advanced logging configuration manager"""
    
    def __init__(self):
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        self.performance_metrics = {}
    
    def setup_logging(self):
        """Setup comprehensive logging configuration"""
        # Remove default handler
        logger.remove()
        
        # Console handler with colors
        logger.add(
            sink=sys.stdout,
            level=settings.LOG_LEVEL,
            format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            colorize=True,
            backtrace=True,
            diagnose=True
        )
        
        # Main application log file
        logger.add(
            sink=self.log_dir / "campusguard.log",
            level=settings.LOG_LEVEL,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation=settings.LOG_ROTATION,
            retention=settings.LOG_RETENTION,
            compression=settings.LOG_COMPRESSION,
            encoding="utf-8",
            enqueue=True
        )
        
        # Error log file (only errors and critical)
        logger.add(
            sink=self.log_dir / "errors.log",
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            encoding="utf-8",
            enqueue=True
        )
        
        # API access log
        logger.add(
            sink=self.log_dir / "api_access.log",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {message}",
            rotation="1 day",
            retention="7 days",
            compression="zip",
            encoding="utf-8",
            enqueue=True,
            filter=lambda record: "API_ACCESS" in record["extra"]
        )
        
        # Performance monitoring log
        logger.add(
            sink=self.log_dir / "performance.log",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {message}",
            rotation="1 day",
            retention="14 days",
            compression="zip",
            encoding="utf-8",
            enqueue=True,
            filter=lambda record: "PERFORMANCE" in record["extra"]
        )
        
        # Device monitoring log
        logger.add(
            sink=self.log_dir / "device_monitoring.log",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {message}",
            rotation="1 day",
            retention="14 days",
            compression="zip",
            encoding="utf-8",
            enqueue=True,
            filter=lambda record: "DEVICE_MONITORING" in record["extra"]
        )
        
        # Alert log
        logger.add(
            sink=self.log_dir / "alerts.log",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {message}",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            encoding="utf-8",
            enqueue=True,
            filter=lambda record: "ALERT" in record["extra"]
        )
        
        # AI conversation log
        logger.add(
            sink=self.log_dir / "ai_conversations.log",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {message}",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            encoding="utf-8",
            enqueue=True,
            filter=lambda record: "AI_CONVERSATION" in record["extra"]
        )
        
        logger.info("Advanced logging configuration initialized")
    
    def log_api_access(self, method: str, url: str, status_code: int, response_time: float, user_id: str = None):
        """Log API access"""
        logger.bind(API_ACCESS=True).info(
            f"{method} {url} - {status_code} - {response_time:.3f}s - User: {user_id or 'Anonymous'}"
        )
    
    def log_performance_metric(self, operation: str, duration: float, metadata: Dict[str, Any] = None):
        """Log performance metric"""
        metadata = metadata or {}
        logger.bind(PERFORMANCE=True).info(
            f"PERF | {operation} | {duration:.3f}s | {metadata}"
        )
        
        # Store in memory for monitoring
        if operation not in self.performance_metrics:
            self.performance_metrics[operation] = []
        
        self.performance_metrics[operation].append({
            'duration': duration,
            'timestamp': datetime.now().isoformat(),
            'metadata': metadata
        })
        
        # Keep only last 100 entries per operation
        if len(self.performance_metrics[operation]) > 100:
            self.performance_metrics[operation] = self.performance_metrics[operation][-100:]
    
    def log_device_monitoring(self, device_ip: str, status: str, metrics_count: int, alerts_count: int):
        """Log device monitoring activity"""
        logger.bind(DEVICE_MONITORING=True).info(
            f"Device {device_ip} | Status: {status} | Metrics: {metrics_count} | Alerts: {alerts_count}"
        )
    
    def log_alert(self, alert_type: str, severity: str, device_ip: str, title: str):
        """Log alert creation"""
        logger.bind(ALERT=True).info(
            f"ALERT | {severity} | {alert_type} | {device_ip} | {title}"
        )
    
    def log_ai_conversation(self, user_id: str, message_length: int, response_length: int, processing_time: float):
        """Log AI conversation activity"""
        logger.bind(AI_CONVERSATION=True).info(
            f"User: {user_id} | Input: {message_length} chars | Output: {response_length} chars | Time: {processing_time:.3f}s"
        )
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance metrics summary"""
        summary = {}
        
        for operation, metrics in self.performance_metrics.items():
            if metrics:
                durations = [m['duration'] for m in metrics]
                summary[operation] = {
                    'count': len(durations),
                    'avg_duration': sum(durations) / len(durations),
                    'min_duration': min(durations),
                    'max_duration': max(durations),
                    'last_execution': metrics[-1]['timestamp']
                }
        
        return summary
    
    def performance_monitor(self, operation_name: str):
        """Decorator for monitoring function performance"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    duration = time.time() - start_time
                    self.log_performance_metric(operation_name, duration, {'success': True})
                    return result
                except Exception as e:
                    duration = time.time() - start_time
                    self.log_performance_metric(operation_name, duration, {'success': False, 'error': str(e)})
                    raise
            return wrapper
        return decorator
    
    def async_performance_monitor(self, operation_name: str):
        """Async decorator for monitoring function performance"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    duration = time.time() - start_time
                    self.log_performance_metric(operation_name, duration, {'success': True})
                    return result
                except Exception as e:
                    duration = time.time() - start_time
                    self.log_performance_metric(operation_name, duration, {'success': False, 'error': str(e)})
                    raise
            return wrapper
        return decorator


# Global logging configuration instance
logging_config = LoggingConfig()

# Setup logging on import
logging_config.setup_logging()

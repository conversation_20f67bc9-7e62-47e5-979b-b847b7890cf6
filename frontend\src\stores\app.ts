import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface AppState {
  collapsed: boolean
  theme: 'light' | 'dark'
  loading: boolean
  title: string
  version: string
}

export const useAppStore = defineStore('app', () => {
  // State
  const collapsed = ref(false)
  const theme = ref<'light' | 'dark'>('light')
  const loading = ref(false)
  const title = ref('CampusGuard')
  const version = ref('1.0.0')

  // Getters
  const isDark = computed(() => theme.value === 'dark')
  const sidebarWidth = computed(() => collapsed.value ? 80 : 200)

  // Actions
  const toggleCollapsed = () => {
    collapsed.value = !collapsed.value
  }

  const setCollapsed = (value: boolean) => {
    collapsed.value = value
  }

  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    updateTheme()
  }

  const setTheme = (value: 'light' | 'dark') => {
    theme.value = value
    updateTheme()
  }

  const updateTheme = () => {
    document.documentElement.setAttribute('data-theme', theme.value)
    localStorage.setItem('theme', theme.value)
  }

  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const initialize = () => {
    // Load theme from localStorage
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | null
    if (savedTheme) {
      setTheme(savedTheme)
    }

    // Load collapsed state from localStorage
    const savedCollapsed = localStorage.getItem('sidebar-collapsed')
    if (savedCollapsed) {
      setCollapsed(JSON.parse(savedCollapsed))
    }
  }

  // Watch for changes and save to localStorage
  const saveSettings = () => {
    localStorage.setItem('theme', theme.value)
    localStorage.setItem('sidebar-collapsed', JSON.stringify(collapsed.value))
  }

  return {
    // State
    collapsed,
    theme,
    loading,
    title,
    version,
    
    // Getters
    isDark,
    sidebarWidth,
    
    // Actions
    toggleCollapsed,
    setCollapsed,
    toggleTheme,
    setTheme,
    setLoading,
    initialize,
    saveSettings
  }
})

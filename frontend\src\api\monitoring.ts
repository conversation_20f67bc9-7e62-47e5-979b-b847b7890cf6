import request from './request'

// 监控服务相关接口类型定义
export interface MonitoringStatus {
  is_running: boolean
  start_time?: string
  uptime?: number
  monitored_devices: number
  active_collectors: number
  last_collection?: string
  performance: {
    cpu_usage: number
    memory_usage: number
    collection_rate: number
  }
  errors: string[]
  warnings: string[]
}

export interface DeviceDiscoveryRequest {
  ip_range: string
  scan_type?: 'ping' | 'snmp' | 'full'
  timeout?: number
  concurrent_limit?: number
}

export interface DeviceDiscoveryResponse {
  task_id: string
  status: 'started' | 'running' | 'completed' | 'failed'
  progress: number
  discovered_devices: DiscoveredDevice[]
  total_scanned: number
  scan_start_time: string
  scan_end_time?: string
  errors: string[]
}

export interface DiscoveredDevice {
  ip_address: string
  hostname?: string
  mac_address?: string
  device_type?: string
  vendor?: string
  model?: string
  os_info?: string
  open_ports: number[]
  snmp_community?: string
  response_time: number
  last_seen: string
  confidence_score: number
}

export interface MonitoringConfig {
  collection_interval: number
  snmp_timeout: number
  snmp_retries: number
  max_concurrent_collections: number
  enable_auto_discovery: boolean
  discovery_interval: number
  alert_thresholds: {
    cpu_threshold: number
    memory_threshold: number
    disk_threshold: number
    network_threshold: number
  }
  retention_policy: {
    performance_data_days: number
    alert_data_days: number
    log_data_days: number
  }
}

export interface CollectorStatus {
  collector_id: string
  collector_type: 'snmp' | 'ping' | 'http' | 'custom'
  status: 'active' | 'inactive' | 'error'
  assigned_devices: number
  last_collection: string
  collection_rate: number
  error_rate: number
  performance_metrics: {
    avg_response_time: number
    success_rate: number
    data_points_collected: number
  }
}

// 监控服务API
export const monitoringApi = {
  /**
   * 获取监控服务状态
   */
  getStatus() {
    return request.get<MonitoringStatus>('/monitoring/status')
  },

  /**
   * 启动监控服务
   */
  startMonitoring() {
    return request.post<{
      success: boolean
      message: string
      timestamp: string
    }>('/monitoring/start')
  },

  /**
   * 停止监控服务
   */
  stopMonitoring() {
    return request.post<{
      success: boolean
      message: string
      timestamp: string
    }>('/monitoring/stop')
  },

  /**
   * 重启监控服务
   */
  restartMonitoring() {
    return request.post<{
      success: boolean
      message: string
      timestamp: string
    }>('/monitoring/restart')
  },

  /**
   * 设备发现
   */
  discoverDevices(data: DeviceDiscoveryRequest) {
    return request.post<DeviceDiscoveryResponse>('/monitoring/discover', data)
  },

  /**
   * 获取设备发现状态
   */
  getDiscoveryStatus(taskId: string) {
    return request.get<DeviceDiscoveryResponse>(`/monitoring/discover/${taskId}`)
  },

  /**
   * 取消设备发现
   */
  cancelDiscovery(taskId: string) {
    return request.delete<{
      success: boolean
      message: string
    }>(`/monitoring/discover/${taskId}`)
  },

  /**
   * 获取监控配置
   */
  getConfig() {
    return request.get<MonitoringConfig>('/monitoring/config')
  },

  /**
   * 更新监控配置
   */
  updateConfig(config: Partial<MonitoringConfig>) {
    return request.put<{
      success: boolean
      message: string
      config: MonitoringConfig
    }>('/monitoring/config', config)
  },

  /**
   * 获取收集器状态
   */
  getCollectorStatus() {
    return request.get<{
      collectors: CollectorStatus[]
      total_collectors: number
      active_collectors: number
      timestamp: string
    }>('/monitoring/collectors')
  },

  /**
   * 重启指定收集器
   */
  restartCollector(collectorId: string) {
    return request.post<{
      success: boolean
      message: string
    }>(`/monitoring/collectors/${collectorId}/restart`)
  },

  /**
   * 获取监控统计信息
   */
  getStatistics(timeRange?: string) {
    return request.get<{
      collection_stats: {
        total_collections: number
        successful_collections: number
        failed_collections: number
        avg_collection_time: number
      }
      device_stats: {
        total_devices: number
        online_devices: number
        offline_devices: number
        warning_devices: number
      }
      performance_stats: {
        avg_cpu_usage: number
        avg_memory_usage: number
        peak_collection_rate: number
      }
      error_stats: {
        total_errors: number
        error_rate: number
        common_errors: Array<{ error: string; count: number }>
      }
      timestamp: string
    }>('/monitoring/statistics', { params: { time_range: timeRange } })
  },

  /**
   * 清理监控数据
   */
  cleanupData(options: {
    cleanup_performance?: boolean
    cleanup_alerts?: boolean
    cleanup_logs?: boolean
    days_to_keep?: number
  }) {
    return request.post<{
      success: boolean
      message: string
      cleaned_records: number
      freed_space: string
    }>('/monitoring/cleanup', options)
  },

  /**
   * 导出监控数据
   */
  exportData(options: {
    data_type: 'performance' | 'alerts' | 'devices' | 'all'
    start_date?: string
    end_date?: string
    format: 'json' | 'csv' | 'excel'
  }) {
    return request.post<{
      download_url: string
      file_size: number
      expires_at: string
    }>('/monitoring/export', options)
  },

  /**
   * 测试设备连接
   */
  testDeviceConnection(deviceId: number) {
    return request.post<{
      success: boolean
      response_time: number
      status: string
      details: Record<string, any>
      timestamp: string
    }>(`/monitoring/test-connection/${deviceId}`)
  }
}

// 监控状态工具函数
export const monitoringUtils = {
  /**
   * 格式化运行时间
   */
  formatUptime(seconds: number): string {
    if (seconds < 60) return `${seconds}秒`
    if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`
    if (seconds < 86400) return `${Math.floor(seconds / 3600)}小时`
    return `${Math.floor(seconds / 86400)}天`
  },

  /**
   * 获取状态颜色
   */
  getStatusColor(status: string): string {
    const colors = {
      active: 'green',
      running: 'green',
      inactive: 'red',
      stopped: 'red',
      error: 'red',
      warning: 'orange',
      completed: 'blue',
      started: 'blue'
    }
    return colors[status as keyof typeof colors] || 'default'
  },

  /**
   * 获取状态图标
   */
  getStatusIcon(status: string): string {
    const icons = {
      active: '✅',
      running: '🟢',
      inactive: '❌',
      stopped: '🔴',
      error: '⚠️',
      warning: '🟡',
      completed: '✅',
      started: '🔄'
    }
    return icons[status as keyof typeof icons] || '❓'
  },

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  /**
   * 验证IP范围格式
   */
  validateIPRange(ipRange: string): boolean {
    // 支持 CIDR 格式 (***********/24) 和范围格式 (***********-*************)
    const cidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/
    const rangeRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)-(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    const singleIPRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    
    return cidrRegex.test(ipRange) || rangeRegex.test(ipRange) || singleIPRegex.test(ipRange)
  },

  /**
   * 格式化时间戳
   */
  formatTimestamp(timestamp?: string): string {
    if (!timestamp) return '-'
    return new Date(timestamp).toLocaleString('zh-CN')
  },

  /**
   * 计算置信度颜色
   */
  getConfidenceColor(score: number): string {
    if (score >= 0.8) return '#52c41a'
    if (score >= 0.6) return '#faad14'
    if (score >= 0.4) return '#fa8c16'
    return '#ff4d4f'
  },

  /**
   * 获取设备类型图标
   */
  getDeviceTypeIcon(deviceType?: string): string {
    const icons = {
      router: '🔀',
      switch: '🔗',
      server: '🖥️',
      firewall: '🛡️',
      access_point: '📡',
      printer: '🖨️',
      camera: '📹',
      phone: '📞',
      other: '❓'
    }
    return icons[deviceType as keyof typeof icons] || icons.other
  }
}

#!/usr/bin/env python3
"""
CampusGuard Setup Script
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Main setup function"""
    print("Setting up CampusGuard development environment...")
    
    # Create virtual environment
    print("Creating Python virtual environment...")
    subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
    
    print("Setup completed successfully!")

if __name__ == "__main__":
    main()

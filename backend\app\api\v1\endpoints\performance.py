"""
Performance monitoring endpoints
"""
from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from loguru import logger

from ....core.database import get_db
from ....models.performance import PerformanceMetric, NetworkTopology, NetworkConnection

router = APIRouter()


@router.get("/metrics")
async def get_performance_metrics(
    device_id: Optional[int] = None,
    metric_name: Optional[str] = None,
    hours: int = Query(24, ge=1, le=168),  # Last 24 hours by default, max 1 week
    db: Session = Depends(get_db)
):
    """Get performance metrics"""
    try:
        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        query = db.query(PerformanceMetric).filter(
            PerformanceMetric.is_active == True,
            PerformanceMetric.timestamp >= start_time.isoformat(),
            PerformanceMetric.timestamp <= end_time.isoformat()
        )
        
        if device_id:
            query = query.filter(PerformanceMetric.device_id == device_id)
        if metric_name:
            query = query.filter(PerformanceMetric.metric_name == metric_name)
            
        metrics = query.order_by(PerformanceMetric.timestamp.desc()).limit(1000).all()
        return metrics
        
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve performance metrics")


@router.get("/topology")
async def get_network_topology(db: Session = Depends(get_db)):
    """Get network topology data"""
    try:
        # Get nodes
        nodes = db.query(NetworkTopology).filter(NetworkTopology.is_active == True).all()
        
        # Get connections
        connections = db.query(NetworkConnection).filter(NetworkConnection.is_active == True).all()
        
        return {
            "nodes": [
                {
                    "id": node.node_id,
                    "type": node.node_type,
                    "name": node.node_name,
                    "ip": node.node_ip,
                    "x": node.x_position,
                    "y": node.y_position,
                    "properties": node.properties,
                    "style": node.style
                }
                for node in nodes
            ],
            "edges": [
                {
                    "source": conn.source_node_id,
                    "target": conn.target_node_id,
                    "type": conn.connection_type,
                    "bandwidth": conn.bandwidth,
                    "latency": conn.latency,
                    "utilization": conn.utilization,
                    "status": conn.status,
                    "properties": conn.properties,
                    "style": conn.style
                }
                for conn in connections
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting network topology: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve network topology")


@router.get("/dashboard")
async def get_dashboard_data(db: Session = Depends(get_db)):
    """Get dashboard performance data"""
    try:
        # Get recent metrics for dashboard
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)  # Last hour
        
        # Get latest metrics per device
        latest_metrics = db.query(PerformanceMetric).filter(
            PerformanceMetric.is_active == True,
            PerformanceMetric.timestamp >= start_time.isoformat()
        ).all()
        
        # Group by device and metric type
        device_metrics = {}
        for metric in latest_metrics:
            device_id = metric.device_id
            if device_id not in device_metrics:
                device_metrics[device_id] = {}
            device_metrics[device_id][metric.metric_name] = metric.metric_value
        
        return {
            "timestamp": end_time.isoformat(),
            "device_metrics": device_metrics,
            "total_devices": len(device_metrics)
        }
        
    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve dashboard data")

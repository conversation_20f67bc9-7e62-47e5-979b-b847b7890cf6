import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// Ant Design Vue
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

// Ant Design X Vue (AI Chat Components)
import AntdX from 'ant-design-x-vue'
import 'ant-design-x-vue/dist/reset.css'

// Global styles
import './styles/index.css'

const app = createApp(App)

// Install plugins
app.use(createPinia())
app.use(router)
app.use(Antd)
app.use(AntdX)

// Mount app
app.mount('#app')

// Initialize WebSocket connection after app is mounted
import { useWebSocketStore } from './stores/websocket'
const wsStore = useWebSocketStore()
wsStore.connect()

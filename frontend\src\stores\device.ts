import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { deviceApi } from '@/api/device'
import type { Device, DeviceStats } from '@/types/device'

export const useDeviceStore = defineStore('device', () => {
  // State
  const devices = ref<Device[]>([])
  const currentDevice = ref<Device | null>(null)
  const stats = ref<DeviceStats | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const onlineDevices = computed(() => 
    devices.value.filter(device => device.status === 'online')
  )

  const offlineDevices = computed(() => 
    devices.value.filter(device => device.status === 'offline')
  )

  const warningDevices = computed(() => 
    devices.value.filter(device => device.status === 'warning')
  )

  const criticalDevices = computed(() => 
    devices.value.filter(device => device.status === 'critical')
  )

  const devicesByType = computed(() => {
    const types: Record<string, Device[]> = {}
    devices.value.forEach(device => {
      if (!types[device.device_type]) {
        types[device.device_type] = []
      }
      types[device.device_type].push(device)
    })
    return types
  })

  // Actions
  const fetchDevices = async (params?: any) => {
    try {
      loading.value = true
      error.value = null
      const response = await deviceApi.getDevices(params)
      devices.value = response.data
    } catch (err: any) {
      error.value = err.message || '获取设备列表失败'
      console.error('Failed to fetch devices:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchDevice = async (id: number) => {
    try {
      loading.value = true
      error.value = null
      const response = await deviceApi.getDevice(id)
      currentDevice.value = response.data
      return response.data
    } catch (err: any) {
      error.value = err.message || '获取设备详情失败'
      console.error('Failed to fetch device:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const createDevice = async (deviceData: Partial<Device>) => {
    try {
      loading.value = true
      error.value = null
      const response = await deviceApi.createDevice(deviceData)
      devices.value.push(response.data)
      return response.data
    } catch (err: any) {
      error.value = err.message || '创建设备失败'
      console.error('Failed to create device:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateDevice = async (id: number, deviceData: Partial<Device>) => {
    try {
      loading.value = true
      error.value = null
      const response = await deviceApi.updateDevice(id, deviceData)
      const index = devices.value.findIndex(device => device.id === id)
      if (index !== -1) {
        devices.value[index] = response.data
      }
      if (currentDevice.value?.id === id) {
        currentDevice.value = response.data
      }
      return response.data
    } catch (err: any) {
      error.value = err.message || '更新设备失败'
      console.error('Failed to update device:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteDevice = async (id: number) => {
    try {
      loading.value = true
      error.value = null
      await deviceApi.deleteDevice(id)
      devices.value = devices.value.filter(device => device.id !== id)
      if (currentDevice.value?.id === id) {
        currentDevice.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除设备失败'
      console.error('Failed to delete device:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchStats = async () => {
    try {
      const response = await deviceApi.getStats()
      stats.value = response.data
    } catch (err: any) {
      console.error('Failed to fetch device stats:', err)
    }
  }

  const testConnection = async (id: number) => {
    try {
      const response = await deviceApi.testConnection(id)
      return response.data
    } catch (err: any) {
      console.error('Failed to test device connection:', err)
      throw err
    }
  }

  const collectMetrics = async (id: number) => {
    try {
      const response = await deviceApi.collectMetrics(id)
      return response.data
    } catch (err: any) {
      console.error('Failed to collect device metrics:', err)
      throw err
    }
  }

  const getDeviceHealth = async (id: number) => {
    try {
      const response = await deviceApi.getHealth(id)
      return response.data
    } catch (err: any) {
      console.error('Failed to get device health:', err)
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentDevice = () => {
    currentDevice.value = null
  }

  return {
    // State
    devices,
    currentDevice,
    stats,
    loading,
    error,

    // Getters
    onlineDevices,
    offlineDevices,
    warningDevices,
    criticalDevices,
    devicesByType,

    // Actions
    fetchDevices,
    fetchDevice,
    createDevice,
    updateDevice,
    deleteDevice,
    fetchStats,
    testConnection,
    collectMetrics,
    getDeviceHealth,
    clearError,
    clearCurrentDevice
  }
})

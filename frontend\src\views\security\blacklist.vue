<template>
  <div class="blacklist-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>IP黑名单管理</h1>
      <a-space>
        <a-button @click="$router.back()">
          <template #icon><ArrowLeftOutlined /></template>
          返回
        </a-button>
        <a-button type="primary" @click="showAddModal">
          <template #icon><PlusOutlined /></template>
          添加黑名单
        </a-button>
      </a-space>
    </div>

    <!-- 搜索和过滤 -->
    <a-card class="filter-card">
      <a-form layout="inline" @finish="handleSearch">
        <a-form-item label="IP地址">
          <a-input
            v-model:value="searchForm.ip"
            placeholder="搜索IP地址"
            style="width: 200px;"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="严重程度">
          <a-select
            v-model:value="searchForm.severity"
            placeholder="选择严重程度"
            style="width: 120px;"
            allow-clear
          >
            <a-select-option value="critical">严重</a-select-option>
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="来源">
          <a-select
            v-model:value="searchForm.source"
            placeholder="选择来源"
            style="width: 120px;"
            allow-clear
          >
            <a-select-option value="builtin">内置</a-select-option>
            <a-select-option value="manual">手动</a-select-option>
            <a-select-option value="auto">自动</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" :loading="loading">
            <template #icon><SearchOutlined /></template>
            搜索
          </a-button>
          <a-button style="margin-left: 8px;" @click="resetSearch">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 统计信息 -->
    <a-row :gutter="16" class="stats-row">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="总黑名单数"
            :value="pagination.total"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix><StopOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="严重威胁"
            :value="blacklistData.filter(item => item.severity === 'critical').length"
            :value-style="{ color: '#ff4d4f' }"
          >
            <template #prefix><ExclamationCircleOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="高危威胁"
            :value="blacklistData.filter(item => item.severity === 'high').length"
            :value-style="{ color: '#fa8c16' }"
          >
            <template #prefix><WarningOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="内置黑名单"
            :value="blacklistData.filter(item => item.source === 'builtin').length"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix><SafetyCertificateOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 黑名单表格 -->
    <a-card title="黑名单列表" class="table-card">
      <template #extra>
        <a-space>
          <a-button @click="refreshData" :loading="loading">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="exportBlacklist">
            <template #icon><DownloadOutlined /></template>
            导出
          </a-button>
          <a-popconfirm
            title="确定要清空所有手动添加的黑名单吗？"
            ok-text="确定"
            cancel-text="取消"
            @confirm="clearManualBlacklist"
          >
            <a-button danger>
              <template #icon><DeleteOutlined /></template>
              清空手动黑名单
            </a-button>
          </a-popconfirm>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="blacklistData"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'ip_address'">
            <a-typography-text copyable>{{ record.ip_address }}</a-typography-text>
          </template>
          <template v-else-if="column.key === 'severity'">
            <a-tag :color="getSeverityColor(record.severity)">
              {{ getSeverityLabel(record.severity) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'source'">
            <a-tag :color="getSourceColor(record.source)">
              {{ getSourceLabel(record.source) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'added_at'">
            {{ formatTime(record.added_at) }}
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button size="small" @click="viewDetails(record)">
                详情
              </a-button>
              <a-popconfirm
                v-if="record.source !== 'builtin'"
                title="确定要移除这个IP吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="removeFromBlacklist(record.ip_address)"
              >
                <a-button size="small" danger>
                  移除
                </a-button>
              </a-popconfirm>
              <a-tooltip v-else title="内置黑名单不能移除">
                <a-button size="small" disabled>
                  移除
                </a-button>
              </a-tooltip>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加黑名单模态框 -->
    <a-modal
      v-model:open="addModalVisible"
      title="添加IP到黑名单"
      @ok="handleAddBlacklist"
      @cancel="resetAddForm"
      :confirm-loading="addLoading"
    >
      <a-form :model="addForm" :rules="addRules" ref="addFormRef" layout="vertical">
        <a-form-item label="IP地址" name="ip_address">
          <a-input
            v-model:value="addForm.ip_address"
            placeholder="请输入IP地址"
          />
        </a-form-item>
        <a-form-item label="严重程度" name="severity">
          <a-select v-model:value="addForm.severity" placeholder="选择严重程度">
            <a-select-option value="critical">严重</a-select-option>
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="原因" name="reason">
          <a-textarea
            v-model:value="addForm.reason"
            placeholder="请输入加入黑名单的原因"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="黑名单详情"
      :footer="null"
      width="600px"
    >
      <a-descriptions v-if="selectedRecord" :column="1" bordered>
        <a-descriptions-item label="IP地址">
          <a-typography-text copyable>{{ selectedRecord.ip_address }}</a-typography-text>
        </a-descriptions-item>
        <a-descriptions-item label="严重程度">
          <a-tag :color="getSeverityColor(selectedRecord.severity)">
            {{ getSeverityLabel(selectedRecord.severity) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="原因">
          {{ selectedRecord.reason }}
        </a-descriptions-item>
        <a-descriptions-item label="来源">
          <a-tag :color="getSourceColor(selectedRecord.source)">
            {{ getSourceLabel(selectedRecord.source) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="添加者">
          {{ selectedRecord.added_by || '系统' }}
        </a-descriptions-item>
        <a-descriptions-item label="添加时间">
          {{ formatTime(selectedRecord.added_at) }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  PlusOutlined,
  SearchOutlined,
  StopOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  SafetyCertificateOutlined,
  ReloadOutlined,
  DownloadOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { threatApi, threatUtils, type BlacklistItem } from '@/api/threat'

// 响应式数据
const loading = ref(false)
const addLoading = ref(false)
const addModalVisible = ref(false)
const detailModalVisible = ref(false)

const blacklistData = ref<BlacklistItem[]>([])
const selectedRecord = ref<BlacklistItem | null>(null)

// 搜索表单
const searchForm = reactive({
  ip: '',
  severity: undefined,
  source: undefined
})

// 添加表单
const addForm = reactive({
  ip_address: '',
  severity: 'medium',
  reason: ''
})

const addFormRef = ref()

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列定义
const columns = [
  { title: 'IP地址', dataIndex: 'ip_address', key: 'ip_address', width: 140 },
  { title: '严重程度', dataIndex: 'severity', key: 'severity', width: 100 },
  { title: '原因', dataIndex: 'reason', key: 'reason', ellipsis: true },
  { title: '来源', dataIndex: 'source', key: 'source', width: 100 },
  { title: '添加者', dataIndex: 'added_by', key: 'added_by', width: 100 },
  { title: '添加时间', dataIndex: 'added_at', key: 'added_at', width: 150 },
  { title: '操作', key: 'actions', width: 120, fixed: 'right' }
]

// 表单验证规则
const addRules = {
  ip_address: [
    { required: true, message: '请输入IP地址' },
    { validator: (rule: any, value: string) => {
      if (value && !threatUtils.validateIP(value)) {
        return Promise.reject('请输入有效的IP地址')
      }
      return Promise.resolve()
    }}
  ],
  severity: [{ required: true, message: '请选择严重程度' }],
  reason: [{ required: true, message: '请输入原因' }]
}

// 工具函数
const getSeverityColor = (severity: string) => {
  const colors = {
    critical: 'red',
    high: 'orange',
    medium: 'gold',
    low: 'blue'
  }
  return colors[severity as keyof typeof colors] || 'default'
}

const getSeverityLabel = (severity: string) => {
  const labels = {
    critical: '严重',
    high: '高',
    medium: '中',
    low: '低'
  }
  return labels[severity as keyof typeof labels] || severity
}

const getSourceColor = (source: string) => {
  const colors = {
    builtin: 'green',
    manual: 'blue',
    auto: 'purple'
  }
  return colors[source as keyof typeof colors] || 'default'
}

const getSourceLabel = (source: string) => {
  const labels = {
    builtin: '内置',
    manual: '手动',
    auto: '自动'
  }
  return labels[source as keyof typeof labels] || source
}

const formatTime = (timestamp?: string) => {
  return threatUtils.formatTimestamp(timestamp)
}

// 数据加载
const loadBlacklist = async () => {
  try {
    loading.value = true
    const params = {
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize
    }
    const response = await threatApi.getBlacklist(params)
    blacklistData.value = response.data.items
    pagination.total = response.data.total
  } catch (error) {
    console.error('加载黑名单失败:', error)
    message.error('加载黑名单失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = () => {
  pagination.current = 1
  loadBlacklist()
}

const resetSearch = () => {
  searchForm.ip = ''
  searchForm.severity = undefined
  searchForm.source = undefined
  pagination.current = 1
  loadBlacklist()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadBlacklist()
}

const refreshData = () => {
  loadBlacklist()
}

const showAddModal = () => {
  addModalVisible.value = true
}

const resetAddForm = () => {
  addForm.ip_address = ''
  addForm.severity = 'medium'
  addForm.reason = ''
  addFormRef.value?.resetFields()
}

const handleAddBlacklist = async () => {
  try {
    await addFormRef.value.validate()
    addLoading.value = true
    
    await threatApi.addToBlacklist({
      ip_address: addForm.ip_address,
      reason: addForm.reason,
      severity: addForm.severity
    })
    
    message.success('添加黑名单成功')
    addModalVisible.value = false
    resetAddForm()
    loadBlacklist()
  } catch (error) {
    console.error('添加黑名单失败:', error)
    message.error('添加黑名单失败')
  } finally {
    addLoading.value = false
  }
}

const removeFromBlacklist = async (ip: string) => {
  try {
    await threatApi.removeFromBlacklist(ip)
    message.success(`IP ${ip} 已从黑名单移除`)
    loadBlacklist()
  } catch (error) {
    console.error('移除黑名单失败:', error)
    message.error('移除黑名单失败')
  }
}

const clearManualBlacklist = async () => {
  try {
    const manualItems = blacklistData.value.filter(item => item.source === 'manual')
    const promises = manualItems.map(item => threatApi.removeFromBlacklist(item.ip_address))
    await Promise.all(promises)
    message.success(`已清空${manualItems.length}个手动添加的黑名单`)
    loadBlacklist()
  } catch (error) {
    console.error('清空手动黑名单失败:', error)
    message.error('清空手动黑名单失败')
  }
}

const viewDetails = (record: BlacklistItem) => {
  selectedRecord.value = record
  detailModalVisible.value = true
}

const exportBlacklist = () => {
  const data = JSON.stringify(blacklistData.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `blacklist_${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  message.success('导出成功')
}

// 组件挂载
onMounted(() => {
  loadBlacklist()
})
</script>

<style scoped>
.blacklist-page {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.filter-card {
  margin-bottom: 16px;
}

.stats-row {
  margin-bottom: 24px;
}

.table-card {
  margin-bottom: 24px;
}
</style>

import request from './request'

export interface PerformanceMetricsParams {
  device_id?: number
  metric_name?: string
  hours?: number
}

export interface PerformanceMetric {
  id: number
  device_id: number
  device_ip: string
  metric_name: string
  metric_value: number
  metric_unit: string
  timestamp: string
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
  is_active: boolean
}

export interface NetworkTopology {
  nodes: TopologyNode[]
  edges: TopologyEdge[]
}

export interface TopologyNode {
  id: string
  type: string
  name: string
  ip: string
  x: number
  y: number
  properties: Record<string, any>
  style: Record<string, any>
}

export interface TopologyEdge {
  source: string
  target: string
  type: string
  bandwidth: number
  latency: number
  utilization: number
  status: string
  properties: Record<string, any>
  style: Record<string, any>
}

export interface DashboardData {
  timestamp: string
  device_metrics: Record<string, Record<string, number>>
  total_devices: number
}

export const performanceApi = {
  // Get performance metrics
  getMetrics(params?: PerformanceMetricsParams) {
    return request.get<PerformanceMetric[]>('/performance/metrics', { params })
  },

  // Get network topology
  getTopology() {
    return request.get<NetworkTopology>('/performance/topology')
  },

  // Get dashboard data
  getDashboard() {
    return request.get<DashboardData>('/performance/dashboard')
  }
}

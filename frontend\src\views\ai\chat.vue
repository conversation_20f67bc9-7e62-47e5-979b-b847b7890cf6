<template>
  <div class="ai-chat">
    <a-row :gutter="16" class="chat-container">
      <!-- Agent Selection Sidebar -->
      <a-col :xs="24" :md="6" class="agent-sidebar">
        <a-card title="AI助手" size="small">
          <a-radio-group 
            v-model:value="selectedAgent" 
            @change="handleAgentChange"
            class="agent-list"
          >
            <a-radio-button 
              v-for="agent in availableAgents" 
              :key="agent.key"
              :value="agent.key"
              class="agent-option"
            >
              <div class="agent-info">
                <div class="agent-name">{{ agent.name }}</div>
                <div class="agent-desc">{{ agent.description }}</div>
              </div>
            </a-radio-button>
          </a-radio-group>
          
          <a-divider />
          
          <!-- Quick Actions -->
          <div class="quick-actions">
            <h4>快速操作</h4>
            <a-space direction="vertical" style="width: 100%">
              <a-button 
                block 
                size="small" 
                @click="sendQuickMessage('查看设备状态')"
              >
                查看设备状态
              </a-button>
              <a-button 
                block 
                size="small" 
                @click="sendQuickMessage('分析网络性能')"
              >
                分析网络性能
              </a-button>
              <a-button 
                block 
                size="small" 
                @click="sendQuickMessage('检查安全威胁')"
              >
                检查安全威胁
              </a-button>
              <a-button 
                block 
                size="small" 
                @click="sendQuickMessage('查看告警信息')"
              >
                查看告警信息
              </a-button>
            </a-space>
          </div>
        </a-card>
      </a-col>
      
      <!-- Chat Interface -->
      <a-col :xs="24" :md="18" class="chat-main">
        <a-card class="chat-card">
          <!-- Chat Header -->
          <template #title>
            <div class="chat-header">
              <a-avatar :src="getCurrentAgentAvatar()" />
              <div class="agent-info">
                <div class="agent-name">{{ getCurrentAgentName() }}</div>
                <div class="agent-status">
                  <a-badge 
                    :status="aiConnected ? 'success' : 'error'" 
                    :text="aiConnected ? '在线' : '离线'"
                  />
                </div>
              </div>
            </div>
          </template>
          
          <template #extra>
            <a-space>
              <a-button 
                type="text" 
                :icon="h(ClearOutlined)" 
                @click="clearChat"
                title="清空对话"
              />
              <a-button 
                type="text" 
                :icon="h(DownloadOutlined)" 
                @click="exportChat"
                title="导出对话"
              />
            </a-space>
          </template>
          
          <!-- Chat Messages -->
          <div class="chat-messages" ref="messagesContainer">
            <div 
              v-for="message in messages" 
              :key="message.id"
              class="message-item"
              :class="{ 'user-message': message.role === 'user', 'ai-message': message.role === 'assistant' }"
            >
              <div class="message-avatar">
                <a-avatar 
                  v-if="message.role === 'user'"
                  :icon="h(UserOutlined)"
                  style="background-color: #1890ff"
                />
                <a-avatar 
                  v-else
                  :src="getCurrentAgentAvatar()"
                />
              </div>
              
              <div class="message-content">
                <div class="message-header">
                  <span class="message-sender">
                    {{ message.role === 'user' ? '用户' : getCurrentAgentName() }}
                  </span>
                  <span class="message-time">
                    {{ formatTime(message.timestamp) }}
                  </span>
                </div>
                
                <div class="message-text">
                  <div v-if="message.role === 'assistant' && message.loading" class="typing-indicator">
                    <a-spin size="small" />
                    <span>AI正在思考中...</span>
                  </div>
                  <div v-else v-html="formatMessage(message.content)"></div>
                </div>
                
                <!-- Message Actions -->
                <div v-if="message.role === 'assistant' && !message.loading" class="message-actions">
                  <a-space>
                    <a-button 
                      type="text" 
                      size="small"
                      :icon="h(CopyOutlined)"
                      @click="copyMessage(message.content)"
                    >
                      复制
                    </a-button>
                    <a-button 
                      type="text" 
                      size="small"
                      :icon="h(LikeOutlined)"
                      @click="likeMessage(message.id)"
                    >
                      有用
                    </a-button>
                    <a-button 
                      type="text" 
                      size="small"
                      :icon="h(DislikeOutlined)"
                      @click="dislikeMessage(message.id)"
                    >
                      无用
                    </a-button>
                  </a-space>
                </div>
              </div>
            </div>
            
            <!-- Empty State -->
            <div v-if="messages.length === 0" class="empty-state">
              <a-empty 
                description="开始与AI助手对话吧！"
                :image="h(RobotOutlined)"
              >
                <template #image>
                  <RobotOutlined style="font-size: 64px; color: #d9d9d9;" />
                </template>
              </a-empty>
            </div>
          </div>
          
          <!-- Chat Input -->
          <div class="chat-input">
            <a-input-group compact>
              <a-textarea
                v-model:value="inputMessage"
                :placeholder="`向${getCurrentAgentName()}提问...`"
                :auto-size="{ minRows: 1, maxRows: 4 }"
                @keydown.enter.exact="handleSend"
                @keydown.enter.shift.exact.prevent="inputMessage += '\n'"
                :disabled="sending"
                class="message-input"
              />
              <a-button 
                type="primary" 
                :loading="sending"
                @click="handleSend"
                :disabled="!inputMessage.trim()"
                class="send-button"
              >
                <template #icon>
                  <SendOutlined />
                </template>
                发送
              </a-button>
            </a-input-group>
            
            <div class="input-tips">
              <span>按 Enter 发送，Shift + Enter 换行</span>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, h } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { aiApi } from '@/api/ai'
import {
  UserOutlined,
  RobotOutlined,
  SendOutlined,
  ClearOutlined,
  DownloadOutlined,
  CopyOutlined,
  LikeOutlined,
  DislikeOutlined
} from '@ant-design/icons-vue'

// Types
interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  loading?: boolean
  agent_type?: string
}

interface AgentInfo {
  key: string
  name: string
  description: string
  avatar?: string
}

// State
const selectedAgent = ref('assistant')
const inputMessage = ref('')
const sending = ref(false)
const aiConnected = ref(true)
const messages = ref<ChatMessage[]>([])
const messagesContainer = ref<HTMLElement>()

// Available agents
const availableAgents: AgentInfo[] = [
  {
    key: 'assistant',
    name: '通用助手',
    description: '回答网络监控相关问题'
  },
  {
    key: 'security',
    name: '安全专家',
    description: '专注于安全威胁分析'
  },
  {
    key: 'performance',
    name: '性能专家',
    description: '专注于性能监控分析'
  }
]

// Computed
const getCurrentAgentName = () => {
  const agent = availableAgents.find(a => a.key === selectedAgent.value)
  return agent?.name || '通用助手'
}

const getCurrentAgentAvatar = () => {
  // Return different avatars for different agents
  const avatarMap: Record<string, string> = {
    assistant: '/avatars/assistant.png',
    security: '/avatars/security.png',
    performance: '/avatars/performance.png'
  }
  return avatarMap[selectedAgent.value] || '/avatars/assistant.png'
}

// Methods
const handleAgentChange = () => {
  // Clear chat when switching agents
  messages.value = []
  addWelcomeMessage()
}

const addWelcomeMessage = () => {
  const welcomeMessages: Record<string, string> = {
    assistant: '您好！我是CampusGuard通用助手，可以帮您查看设备状态、分析网络问题。有什么可以帮您的吗？',
    security: '您好！我是安全分析专家，专门负责网络安全威胁检测和分析。请告诉我您需要分析什么安全问题？',
    performance: '您好！我是性能分析专家，专门负责网络性能监控和优化建议。请告诉我您需要分析什么性能问题？'
  }
  
  const welcomeMessage: ChatMessage = {
    id: Date.now().toString(),
    role: 'assistant',
    content: welcomeMessages[selectedAgent.value] || welcomeMessages.assistant,
    timestamp: new Date().toISOString(),
    agent_type: selectedAgent.value
  }
  
  messages.value.push(welcomeMessage)
  scrollToBottom()
}

const handleSend = async () => {
  if (!inputMessage.value.trim() || sending.value) return
  
  const userMessage: ChatMessage = {
    id: Date.now().toString(),
    role: 'user',
    content: inputMessage.value.trim(),
    timestamp: new Date().toISOString()
  }
  
  messages.value.push(userMessage)
  
  // Create loading message
  const loadingMessage: ChatMessage = {
    id: (Date.now() + 1).toString(),
    role: 'assistant',
    content: '',
    timestamp: new Date().toISOString(),
    loading: true,
    agent_type: selectedAgent.value
  }
  
  messages.value.push(loadingMessage)
  
  const messageToSend = inputMessage.value
  inputMessage.value = ''
  sending.value = true
  
  await scrollToBottom()
  
  try {
    const response = await aiApi.chatWithAgent(selectedAgent.value, messageToSend)
    
    // Remove loading message
    messages.value = messages.value.filter(m => m.id !== loadingMessage.id)
    
    // Add AI response
    const aiMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'assistant',
      content: response.response,
      timestamp: response.timestamp,
      agent_type: selectedAgent.value
    }
    
    messages.value.push(aiMessage)
    
  } catch (error: any) {
    // Remove loading message
    messages.value = messages.value.filter(m => m.id !== loadingMessage.id)
    
    // Add error message
    const errorMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'assistant',
      content: `抱歉，发生了错误：${error.message || '未知错误'}`,
      timestamp: new Date().toISOString(),
      agent_type: selectedAgent.value
    }
    
    messages.value.push(errorMessage)
    
    message.error('发送消息失败')
  } finally {
    sending.value = false
    await scrollToBottom()
  }
}

const sendQuickMessage = (quickMessage: string) => {
  inputMessage.value = quickMessage
  handleSend()
}

const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('HH:mm:ss')
}

const formatMessage = (content: string) => {
  // Simple markdown-like formatting
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    message.success('已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

const likeMessage = (messageId: string) => {
  message.success('感谢您的反馈！')
}

const dislikeMessage = (messageId: string) => {
  message.info('感谢您的反馈，我们会持续改进！')
}

const clearChat = () => {
  messages.value = []
  addWelcomeMessage()
}

const exportChat = () => {
  const chatContent = messages.value
    .filter(m => !m.loading)
    .map(m => `[${formatTime(m.timestamp)}] ${m.role === 'user' ? '用户' : getCurrentAgentName()}: ${m.content}`)
    .join('\n\n')
  
  const blob = new Blob([chatContent], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chat-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

// Lifecycle
onMounted(() => {
  addWelcomeMessage()
})
</script>

<style scoped>
.ai-chat {
  padding: 24px;
  background: var(--bg-color);
  min-height: 100vh;
}

.chat-container {
  height: calc(100vh - 48px);
}

.agent-sidebar {
  height: 100%;
}

.agent-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.agent-option {
  margin-bottom: 8px;
  height: auto;
  padding: 12px;
  text-align: left;
}

.agent-info .agent-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.agent-info .agent-desc {
  font-size: 12px;
  color: var(--text-secondary);
}

.quick-actions h4 {
  margin-bottom: 12px;
  font-size: 14px;
}

.chat-main {
  height: 100%;
}

.chat-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-header .agent-info .agent-name {
  font-weight: 500;
}

.chat-header .agent-info .agent-status {
  font-size: 12px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  max-height: calc(100vh - 300px);
}

.message-item {
  display: flex;
  margin-bottom: 24px;
  gap: 12px;
}

.user-message {
  flex-direction: row-reverse;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.user-message .message-content {
  text-align: right;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--text-secondary);
}

.user-message .message-header {
  flex-direction: row-reverse;
}

.message-text {
  background: var(--card-bg);
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid var(--border-color-light);
  line-height: 1.6;
}

.user-message .message-text {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
}

.message-actions {
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.user-message .message-actions {
  text-align: right;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.chat-input {
  border-top: 1px solid var(--border-color-light);
  padding-top: 16px;
}

.message-input {
  border-radius: 8px 0 0 8px;
}

.send-button {
  border-radius: 0 8px 8px 0;
  height: auto;
}

.input-tips {
  margin-top: 8px;
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
  .ai-chat {
    padding: 16px;
  }
  
  .chat-container {
    height: auto;
  }
  
  .agent-sidebar {
    margin-bottom: 16px;
  }
  
  .message-content {
    max-width: 85%;
  }
}
</style>

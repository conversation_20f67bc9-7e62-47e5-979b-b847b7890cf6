<template>
  <div class="ai-chat">
    <a-row :gutter="16" class="chat-container">
      <!-- Agent Selection Sidebar -->
      <a-col :xs="24" :md="6" class="agent-sidebar">
        <a-card title="AI助手" size="small">
          <!-- Agent Selection -->
          <div class="agent-selection">
            <a-select
              v-model:value="selectedAgent"
              @change="handleAgentChange"
              style="width: 100%"
              size="large"
            >
              <a-select-option
                v-for="agent in availableAgents"
                :key="agent.key"
                :value="agent.key"
              >
                <div class="agent-option">
                  <a-avatar :src="agent.avatar" size="small" style="margin-right: 8px;" />
                  <div class="agent-info">
                    <div class="agent-name">{{ agent.name }}</div>
                    <div class="agent-desc">{{ agent.description }}</div>
                  </div>
                </div>
              </a-select-option>
            </a-select>
          </div>

          <a-divider />

          <!-- Quick Prompts -->
          <div class="quick-prompts">
            <h4>快速提示</h4>
            <div class="prompts-list">
              <a-button
                v-for="prompt in quickPrompts"
                :key="prompt.key"
                type="text"
                size="small"
                @click="handlePromptClick(prompt)"
                class="prompt-button"
              >
                <component :is="prompt.icon" />
                {{ prompt.label }}
              </a-button>
            </div>
          </div>

          <a-divider />

          <!-- Connection Status -->
          <div class="connection-status">
            <a-badge
              :status="aiConnected ? 'success' : 'error'"
              :text="aiConnected ? '连接正常' : '连接断开'"
            />
          </div>
        </a-card>
      </a-col>

      <!-- Chat Interface -->
      <a-col :xs="24" :md="18" class="chat-main">
        <a-card class="chat-card">
          <!-- Chat Header -->
          <template #title>
            <div class="chat-header">
              <a-avatar :src="getCurrentAgent().avatar" />
              <div class="agent-info">
                <div class="agent-name">{{ getCurrentAgent().name }}</div>
                <div class="agent-desc">{{ getCurrentAgent().description }}</div>
              </div>
            </div>
          </template>

          <template #extra>
            <a-space>
              <a-button
                type="text"
                :icon="h(ClearOutlined)"
                @click="clearChat"
                title="清空对话"
              />
              <a-button
                type="text"
                :icon="h(DownloadOutlined)"
                @click="exportChat"
                title="导出对话"
              />
            </a-space>
          </template>

          <!-- AI Chat Interface using ant-design-x-vue -->
          <div class="ai-chat-container">
            <!-- Conversations Management -->
            <AXConversations
              :items="conversations"
              :active-key="activeConversationKey"
              @active-change="handleConversationChange"
              @create="handleCreateConversation"
              class="conversations-panel"
            >
              <template #title="{ item }">
                <span>{{ item.title || `与${getCurrentAgent().name}的对话` }}</span>
              </template>
            </AXConversations>

            <!-- Messages Display Area -->
            <div class="messages-container" ref="messagesContainer">
              <AXBubble
                v-for="message in currentMessages"
                :key="message.id"
                :content="message.content"
                :type="message.type"
                :avatar="message.avatar"
                :status="message.status"
                :typing="message.typing"
                :actions="message.type === 'ai' ? bubbleActions : undefined"
                class="message-bubble"
              />

              <!-- Empty State -->
              <div v-if="currentMessages.length === 0" class="empty-state">
                <a-empty description="开始与AI助手对话吧！">
                  <template #image>
                    <RobotOutlined style="font-size: 64px; color: #d9d9d9;" />
                  </template>
                </a-empty>
              </div>
            </div>

            <!-- Input Area using AXPrompt -->
            <AXPrompt
              v-model="inputValue"
              :loading="isLoading"
              :placeholder="`向${getCurrentAgent().name}提问...`"
              :auto-size="{ minRows: 1, maxRows: 4 }"
              @submit="handleSubmit"
              @clear="handleClear"
              class="chat-input-prompt"
            >
              <template #actions>
                <a-space>
                  <a-button
                    type="text"
                    size="small"
                    @click="handleClearHistory"
                    :icon="h(ClearOutlined)"
                  >
                    清空历史
                  </a-button>
                  <a-button
                    type="text"
                    size="small"
                    @click="showQuickPrompts = !showQuickPrompts"
                    :icon="h(RobotOutlined)"
                  >
                    快速提示
                  </a-button>
                </a-space>
              </template>
            </AXPrompt>

            <!-- Quick Prompts Suggestions -->
            <div v-if="showQuickPrompts" class="quick-prompts-overlay">
              <div class="quick-prompts-content">
                <h4>快速提示</h4>
                <div class="prompts-grid">
                  <a-button
                    v-for="prompt in quickPrompts"
                    :key="prompt.key"
                    type="text"
                    size="small"
                    @click="handlePromptClick(prompt)"
                    class="prompt-button"
                  >
                    <component :is="prompt.icon" />
                    {{ prompt.label }}
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, h, computed } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { aiApi } from '@/api/ai'
import {
  UserOutlined,
  RobotOutlined,
  ClearOutlined,
  DownloadOutlined,
  SecurityScanOutlined,
  LineChartOutlined,
  AlertOutlined,
  CopyOutlined
} from '@ant-design/icons-vue'

// Import ant-design-x-vue components
import {
  Conversations as AXConversations,
  Bubble as AXBubble,
  Prompt as AXPrompt
} from 'ant-design-x-vue'

// Types
interface Message {
  id: string
  content: string
  type: 'user' | 'ai'
  avatar?: string
  status?: 'loading' | 'success' | 'error'
  typing?: boolean
  timestamp: string
  agent_type?: string
}

interface Conversation {
  key: string
  title: string
  messages: Message[]
}

// State
const selectedAgent = ref('general')
const inputValue = ref('')
const isLoading = ref(false)
const aiConnected = ref(true)
const messagesContainer = ref()
const showQuickPrompts = ref(false)

// Conversations management
const conversations = ref<Conversation[]>([
  {
    key: 'default',
    title: '默认对话',
    messages: []
  }
])

const activeConversationKey = ref('default')

// Suggestion items for auto-completion
const suggestionItems = ref([
  { key: 'device-status', label: '查看设备状态', value: '查看设备状态' },
  { key: 'network-performance', label: '分析网络性能', value: '分析网络性能' },
  { key: 'security-threats', label: '检查安全威胁', value: '检查安全威胁' },
  { key: 'alert-info', label: '查看告警信息', value: '查看告警信息' },
  { key: 'topology-view', label: '查看网络拓扑', value: '查看网络拓扑' },
  { key: 'performance-report', label: '生成性能报告', value: '生成性能报告' }
])

// Available agents (updated to match backend)
const availableAgents = [
  {
    key: 'general',
    name: '通用助手',
    description: '网络监控通用AI助手，提供设备状态、告警概览等基础信息',
    avatar: '/avatars/general.png'
  },
  {
    key: 'security',
    name: '安全专家',
    description: '网络安全分析专家，专注威胁检测、安全事件分析和防护建议',
    avatar: '/avatars/security.png'
  },
  {
    key: 'performance',
    name: '性能专家',
    description: '网络性能分析专家，专注性能监控、瓶颈分析和优化建议',
    avatar: '/avatars/performance.png'
  }
]

// Quick prompts for different agents
const quickPrompts = ref([
  {
    key: 'device-status',
    label: '查看设备状态',
    icon: h(LineChartOutlined)
  },
  {
    key: 'network-performance',
    label: '分析网络性能',
    icon: h(LineChartOutlined)
  },
  {
    key: 'security-threats',
    label: '检查安全威胁',
    icon: h(SecurityScanOutlined)
  },
  {
    key: 'alert-info',
    label: '查看告警信息',
    icon: h(AlertOutlined)
  }
])

// Computed properties
const getCurrentAgent = () => {
  return availableAgents.find(a => a.key === selectedAgent.value) || availableAgents[0]
}

const currentMessages = computed(() => {
  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )
  return currentConv ? currentConv.messages : []
})

// Bubble actions for AI messages
const bubbleActions = [
  {
    icon: h(CopyOutlined),
    tooltip: '复制',
    onClick: (message: Message) => copyMessage(message.content)
  },
  {
    icon: h(RobotOutlined),
    tooltip: '重新生成',
    onClick: (message: Message) => regenerateMessage(message)
  }
]

// Methods
const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('HH:mm:ss')
}

const handleAgentChange = () => {
  // Clear current conversation when switching agents
  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )
  if (currentConv) {
    currentConv.messages = []
  }
  addWelcomeMessage()
}

const addWelcomeMessage = () => {
  const welcomeMessages = {
    general: '您好！我是CampusGuard通用助手，可以帮您查看设备状态、分析网络问题。有什么可以帮您的吗？',
    security: '您好！我是安全分析专家，专门负责网络安全威胁检测和分析。请告诉我您需要分析什么安全问题？',
    performance: '您好！我是性能分析专家，专门负责网络性能监控和优化建议。请告诉我您需要分析什么性能问题？'
  }

  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )

  if (currentConv) {
    const welcomeMessage: Message = {
      id: Date.now().toString(),
      content: welcomeMessages[selectedAgent.value as keyof typeof welcomeMessages] || welcomeMessages.general,
      type: 'ai',
      avatar: getCurrentAgent().avatar,
      status: 'success',
      timestamp: new Date().toISOString(),
      agent_type: selectedAgent.value
    }

    currentConv.messages.push(welcomeMessage)
    scrollToBottom()
  }
}

const handlePromptClick = (item: any) => {
  const promptMessages = {
    'device-status': '查看设备状态',
    'network-performance': '分析网络性能',
    'security-threats': '检查安全威胁',
    'alert-info': '查看告警信息'
  }

  inputValue.value = promptMessages[item.key as keyof typeof promptMessages] || item.label
  showQuickPrompts.value = false
  handleSubmit(inputValue.value)
}

// New conversation management methods
const handleConversationChange = (key: string) => {
  activeConversationKey.value = key
}

const handleCreateConversation = () => {
  const newKey = `conv_${Date.now()}`
  const newConversation: Conversation = {
    key: newKey,
    title: `与${getCurrentAgent().name}的对话`,
    messages: []
  }
  conversations.value.push(newConversation)
  activeConversationKey.value = newKey
  addWelcomeMessage()
}

// Main submit handler for AXPrompt
const handleSubmit = async (value: string) => {
  if (!value.trim() || isLoading.value) return

  isLoading.value = true

  // Get current conversation
  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )

  if (!currentConv) return

  // Add user message
  const userMessage: Message = {
    id: Date.now().toString(),
    content: value,
    type: 'user',
    avatar: '/avatars/user.png',
    status: 'success',
    timestamp: new Date().toISOString()
  }
  currentConv.messages.push(userMessage)

  // Add AI thinking status
  const aiMessage: Message = {
    id: (Date.now() + 1).toString(),
    content: '',
    type: 'ai',
    avatar: getCurrentAgent().avatar,
    status: 'loading',
    typing: true,
    timestamp: new Date().toISOString(),
    agent_type: selectedAgent.value
  }
  currentConv.messages.push(aiMessage)

  await scrollToBottom()

  try {
    // Call AI API
    const response = await aiApi.chatWithAgent(selectedAgent.value, value)

    if (response.response) {
      // Update AI reply
      aiMessage.content = response.response
      aiMessage.status = 'success'
      aiMessage.typing = false
    } else {
      aiMessage.content = '抱歉，AI服务暂时不可用，请稍后重试。'
      aiMessage.status = 'error'
      aiMessage.typing = false
    }
  } catch (error: any) {
    aiMessage.content = `抱歉，发生了错误：${error.message || '未知错误'}`
    aiMessage.status = 'error'
    aiMessage.typing = false
    message.error('发送消息失败')
  } finally {
    isLoading.value = false
    await scrollToBottom()
  }
}

const handleClear = () => {
  inputValue.value = ''
}

const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    message.success('已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

const regenerateMessage = async (messageToRegenerate: Message) => {
  // Find the current conversation
  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )

  if (!currentConv) return

  // Find the user message that triggered this AI response
  const messageIndex = currentConv.messages.findIndex(m => m.id === messageToRegenerate.id)
  if (messageIndex > 0) {
    const userMessage = currentConv.messages[messageIndex - 1]
    if (userMessage.type === 'user') {
      // Remove the AI message and regenerate
      currentConv.messages.splice(messageIndex, 1)
      await handleSubmit(userMessage.content)
    }
  }
}

const handleClearHistory = () => {
  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )
  if (currentConv) {
    currentConv.messages = []
    addWelcomeMessage()
  }
}

const clearChat = () => {
  handleClearHistory()
}

const exportChat = () => {
  const formatTimeLocal = (timestamp: string) => {
    return dayjs(timestamp).format('HH:mm:ss')
  }

  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )

  if (!currentConv) return

  const chatContent = currentConv.messages
    .filter(m => !m.typing)
    .map(m => `[${formatTimeLocal(m.timestamp)}] ${m.type === 'user' ? '用户' : getCurrentAgent().name}: ${m.content}`)
    .join('\n\n')

  const blob = new Blob([chatContent], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chat-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

// Lifecycle
onMounted(() => {
  addWelcomeMessage()
})
</script>

<style scoped>
.ai-chat {
  padding: 24px;
  background: var(--bg-color);
  min-height: 100vh;
}

.chat-container {
  height: calc(100vh - 48px);
}

.agent-sidebar {
  height: 100%;
}

.agent-option {
  margin-bottom: 8px;
  height: auto;
  padding: 12px;
  text-align: left;
}

.agent-info .agent-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.agent-info .agent-desc {
  font-size: 12px;
  color: var(--text-secondary);
}

.quick-prompts h4 {
  margin-bottom: 12px;
  font-size: 14px;
}

.prompt-button {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  width: 100%;
  text-align: left;
}

.chat-main {
  height: 100%;
}

.chat-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-header .agent-info .agent-name {
  font-weight: 500;
}

.chat-header .agent-info .agent-desc {
  font-size: 12px;
  color: var(--text-secondary);
}

/* AI Chat Container Styles */
.ai-chat-container {
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.conversations-panel {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  padding: 8px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  max-height: calc(100vh - 400px);
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  padding: 16px;
}

.message-bubble {
  margin-bottom: 16px;
}

.chat-input-prompt {
  border-top: 1px solid var(--border-color-light);
  padding-top: 16px;
}

.quick-prompts-overlay {
  position: absolute;
  bottom: 100px;
  left: 0;
  right: 0;
  background: var(--card-bg);
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.quick-prompts-content h4 {
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
}

.prompts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.prompts-grid .prompt-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.prompts-grid .prompt-button:hover {
  background-color: var(--hover-bg);
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
}

.connection-status {
  margin-top: 16px;
}

/* Responsive */
@media (max-width: 768px) {
  .ai-chat {
    padding: 16px;
  }

  .chat-container {
    height: auto;
  }

  .agent-sidebar {
    margin-bottom: 16px;
  }

  .ai-chat-container {
    height: calc(100vh - 150px);
  }

  .messages-container {
    max-height: calc(100vh - 350px);
  }

  .conversations-panel {
    max-height: 80px;
  }

  .prompts-grid {
    grid-template-columns: 1fr;
  }

  .quick-prompts-overlay {
    bottom: 80px;
  }
}
</style>

<template>
  <div class="ai-chat">
    <a-row :gutter="16" class="chat-container">
      <!-- Agent Selection Sidebar -->
      <a-col :xs="24" :md="6" class="agent-sidebar">
        <a-card title="AI助手" size="small">
          <!-- Agent Selection -->
          <div class="agent-selection">
            <a-select
              v-model:value="selectedAgent"
              @change="handleAgentChange"
              style="width: 100%"
              size="large"
            >
              <a-select-option
                v-for="agent in availableAgents"
                :key="agent.key"
                :value="agent.key"
              >
                <div class="agent-option">
                  <a-avatar :src="agent.avatar" size="small" style="margin-right: 8px;" />
                  <div class="agent-info">
                    <div class="agent-name">{{ agent.name }}</div>
                    <div class="agent-desc">{{ agent.description }}</div>
                  </div>
                </div>
              </a-select-option>
            </a-select>
          </div>

          <a-divider />

          <!-- Quick Prompts -->
          <div class="quick-prompts">
            <h4>快速提示</h4>
            <div class="prompts-list">
              <a-button
                v-for="prompt in quickPrompts"
                :key="prompt.key"
                type="text"
                size="small"
                @click="handlePromptClick(prompt)"
                class="prompt-button"
              >
                <component :is="prompt.icon" />
                {{ prompt.label }}
              </a-button>
            </div>
          </div>

          <a-divider />

          <!-- Connection Status -->
          <div class="connection-status">
            <a-badge
              :status="aiConnected ? 'success' : 'error'"
              :text="aiConnected ? '连接正常' : '连接断开'"
            />
          </div>
        </a-card>
      </a-col>

      <!-- Chat Interface -->
      <a-col :xs="24" :md="18" class="chat-main">
        <a-card class="chat-card">
          <!-- Chat Header -->
          <template #title>
            <div class="chat-header">
              <a-avatar :src="getCurrentAgent().avatar" />
              <div class="agent-info">
                <div class="agent-name">{{ getCurrentAgent().name }}</div>
                <div class="agent-desc">{{ getCurrentAgent().description }}</div>
              </div>
            </div>
          </template>

          <template #extra>
            <a-space>
              <a-button
                type="text"
                :icon="h(ClearOutlined)"
                @click="clearChat"
                title="清空对话"
              />
              <a-button
                type="text"
                :icon="h(DownloadOutlined)"
                @click="exportChat"
                title="导出对话"
              />
            </a-space>
          </template>

          <!-- Chat Interface -->
          <div class="chat-interface">
            <!-- Messages Display -->
            <div class="chat-messages" ref="messagesContainer">
              <div
                v-for="message in messages"
                :key="message.id"
                :class="['message-item', message.role === 'user' ? 'user-message' : 'assistant-message']"
              >
                <div class="message-avatar">
                  <a-avatar
                    v-if="message.role === 'user'"
                    :icon="h(UserOutlined)"
                    size="small"
                  />
                  <a-avatar
                    v-else
                    :src="getCurrentAgent().avatar"
                    size="small"
                  />
                </div>
                <div class="message-content">
                  <div class="message-bubble">
                    <div v-if="message.loading" class="message-loading">
                      <a-spin size="small" />
                      <span>AI正在思考中...</span>
                    </div>
                    <div v-else class="message-text">
                      {{ message.content }}
                    </div>
                  </div>
                  <div class="message-actions" v-if="message.role === 'assistant' && !message.loading">
                    <a-button
                      type="text"
                      size="small"
                      :icon="h(CopyOutlined)"
                      @click="copyMessage(message.content)"
                      title="复制"
                    />
                    <a-button
                      type="text"
                      size="small"
                      :icon="h(RobotOutlined)"
                      @click="regenerateMessage(message)"
                      title="重新生成"
                    />
                  </div>
                  <div class="message-time">
                    {{ formatTime(message.timestamp) }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-if="messages.length === 0" class="empty-state">
              <a-empty description="开始与AI助手对话吧！">
                <template #image>
                  <RobotOutlined style="font-size: 64px; color: #d9d9d9;" />
                </template>
              </a-empty>
            </div>

            <!-- Input Area -->
            <div class="chat-input-area">
              <!-- Suggestions -->
              <div v-if="showSuggestions" class="suggestions-dropdown">
                <div
                  v-for="suggestion in filteredSuggestions"
                  :key="suggestion.key"
                  class="suggestion-item"
                  @click="handleSuggestionClick(suggestion)"
                >
                  {{ suggestion.label }}
                </div>
              </div>

              <!-- Input -->
              <div class="chat-input">
                <a-input
                  v-model:value="inputMessage"
                  :placeholder="`向${getCurrentAgent().name}提问...`"
                  @keydown.enter="handleSend"
                  @input="handleInputChange"
                  size="large"
                  class="message-input"
                />
                <a-button
                  type="primary"
                  :loading="sending"
                  @click="handleSend"
                  size="large"
                  class="send-button"
                >
                  发送
                </a-button>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, h, computed } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { aiApi } from '@/api/ai'
import {
  UserOutlined,
  RobotOutlined,
  ClearOutlined,
  DownloadOutlined,
  SecurityScanOutlined,
  LineChartOutlined,
  AlertOutlined,
  CopyOutlined
} from '@ant-design/icons-vue'

// 配置和常量

// State
const selectedAgent = ref('assistant')
const inputMessage = ref('')
const sending = ref(false)
const aiConnected = ref(true)
const messages = ref([])
const messagesContainer = ref()
const showSuggestions = ref(false)

// Suggestion items for auto-completion
const suggestionItems = ref([
  { key: 'device-status', label: '查看设备状态', value: '查看设备状态' },
  { key: 'network-performance', label: '分析网络性能', value: '分析网络性能' },
  { key: 'security-threats', label: '检查安全威胁', value: '检查安全威胁' },
  { key: 'alert-info', label: '查看告警信息', value: '查看告警信息' },
  { key: 'topology-view', label: '查看网络拓扑', value: '查看网络拓扑' },
  { key: 'performance-report', label: '生成性能报告', value: '生成性能报告' }
])

// Available agents
const availableAgents = [
  {
    key: 'assistant',
    name: '通用助手',
    description: '回答网络监控相关问题',
    avatar: '/avatars/assistant.png'
  },
  {
    key: 'security',
    name: '安全专家',
    description: '专注于安全威胁分析',
    avatar: '/avatars/security.png'
  },
  {
    key: 'performance',
    name: '性能专家',
    description: '专注于性能监控分析',
    avatar: '/avatars/performance.png'
  }
]

// Quick prompts for different agents
const quickPrompts = ref([
  {
    key: 'device-status',
    label: '查看设备状态',
    icon: h(LineChartOutlined)
  },
  {
    key: 'network-performance',
    label: '分析网络性能',
    icon: h(LineChartOutlined)
  },
  {
    key: 'security-threats',
    label: '检查安全威胁',
    icon: h(SecurityScanOutlined)
  },
  {
    key: 'alert-info',
    label: '查看告警信息',
    icon: h(AlertOutlined)
  }
])

// Computed
const getCurrentAgent = () => {
  return availableAgents.find(a => a.key === selectedAgent.value) || availableAgents[0]
}

// Computed properties
const filteredSuggestions = computed(() => {
  if (!inputMessage.value || !showSuggestions.value) return []
  return suggestionItems.value.filter(item =>
    item.label.toLowerCase().includes(inputMessage.value.toLowerCase())
  )
})

// Methods
// Format time helper
const formatTime = (timestamp) => {
  return dayjs(timestamp).format('HH:mm:ss')
}

// Methods
const handleAgentChange = () => {
  // Clear chat when switching agents
  messages.value = []
  addWelcomeMessage()
  updateBubbleItems()
}

const addWelcomeMessage = () => {
  const welcomeMessages = {
    assistant: '您好！我是CampusGuard通用助手，可以帮您查看设备状态、分析网络问题。有什么可以帮您的吗？',
    security: '您好！我是安全分析专家，专门负责网络安全威胁检测和分析。请告诉我您需要分析什么安全问题？',
    performance: '您好！我是性能分析专家，专门负责网络性能监控和优化建议。请告诉我您需要分析什么性能问题？'
  }

  const welcomeMessage = {
    id: Date.now().toString(),
    role: 'assistant',
    content: welcomeMessages[selectedAgent.value] || welcomeMessages.assistant,
    timestamp: new Date().toISOString(),
    agent_type: selectedAgent.value
  }

  messages.value.push(welcomeMessage)
  updateBubbleItems()
  scrollToBottom()
}

const handlePromptClick = (item) => {
  const promptMessages = {
    'device-status': '查看设备状态',
    'network-performance': '分析网络性能',
    'security-threats': '检查安全威胁',
    'alert-info': '查看告警信息'
  }

  inputMessage.value = promptMessages[item.key] || item.label
  handleSend()
}

const handleSend = async (messageText) => {
  const textToSend = messageText || inputMessage.value.trim()
  if (!textToSend || sending.value) return

  const userMessage = {
    id: Date.now().toString(),
    role: 'user',
    content: textToSend,
    timestamp: new Date().toISOString()
  }

  messages.value.push(userMessage)

  // Create loading message
  const loadingMessage = {
    id: (Date.now() + 1).toString(),
    role: 'assistant',
    content: 'AI正在思考中...',
    timestamp: new Date().toISOString(),
    loading: true,
    agent_type: selectedAgent.value
  }

  messages.value.push(loadingMessage)
  updateBubbleItems()

  inputMessage.value = ''
  sending.value = true

  await scrollToBottom()

  try {
    const response = await aiApi.chatWithAgent(selectedAgent.value, textToSend)

    // Remove loading message
    messages.value = messages.value.filter(m => m.id !== loadingMessage.id)

    // Add AI response
    const aiMessage = {
      id: Date.now().toString(),
      role: 'assistant',
      content: response.response,
      timestamp: response.timestamp,
      agent_type: selectedAgent.value
    }

    messages.value.push(aiMessage)

  } catch (error) {
    // Remove loading message
    messages.value = messages.value.filter(m => m.id !== loadingMessage.id)

    // Add error message
    const errorMessage = {
      id: Date.now().toString(),
      role: 'assistant',
      content: `抱歉，发生了错误：${error.message || '未知错误'}`,
      timestamp: new Date().toISOString(),
      agent_type: selectedAgent.value
    }

    messages.value.push(errorMessage)

    message.error('发送消息失败')
  } finally {
    sending.value = false
    updateBubbleItems()
    await scrollToBottom()
  }
}

const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const copyMessage = async (content) => {
  try {
    await navigator.clipboard.writeText(content)
    message.success('已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

const regenerateMessage = async (messageToRegenerate) => {
  // Find the user message that triggered this AI response
  const messageIndex = messages.value.findIndex(m => m.id === messageToRegenerate.id)
  if (messageIndex > 0) {
    const userMessage = messages.value[messageIndex - 1]
    if (userMessage.role === 'user') {
      // Remove the AI message and regenerate
      messages.value.splice(messageIndex, 1)
      await handleSend(userMessage.content)
    }
  }
}

const handleInputChange = (value) => {
  inputMessage.value = value
  // Show suggestions when user types "/"
  showSuggestions.value = value.endsWith('/')
}

const handleSuggestionClick = (suggestion) => {
  inputMessage.value = suggestion.value
  showSuggestions.value = false
  handleSend()
}

const clearChat = () => {
  messages.value = []
  addWelcomeMessage()
}

const exportChat = () => {
  const formatTimeLocal = (timestamp) => {
    return dayjs(timestamp).format('HH:mm:ss')
  }

  const chatContent = messages.value
    .filter(m => !m.loading)
    .map(m => `[${formatTimeLocal(m.timestamp)}] ${m.role === 'user' ? '用户' : getCurrentAgent().name}: ${m.content}`)
    .join('\n\n')

  const blob = new Blob([chatContent], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chat-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

// Lifecycle
onMounted(() => {
  addWelcomeMessage()
})
</script>

<style scoped>
.ai-chat {
  padding: 24px;
  background: var(--bg-color);
  min-height: 100vh;
}

.chat-container {
  height: calc(100vh - 48px);
}

.agent-sidebar {
  height: 100%;
}

.agent-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.agent-option {
  margin-bottom: 8px;
  height: auto;
  padding: 12px;
  text-align: left;
}

.agent-info .agent-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.agent-info .agent-desc {
  font-size: 12px;
  color: var(--text-secondary);
}

.quick-actions h4 {
  margin-bottom: 12px;
  font-size: 14px;
}

.chat-main {
  height: 100%;
}

.chat-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-header .agent-info .agent-name {
  font-weight: 500;
}

.chat-header .agent-info .agent-status {
  font-size: 12px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  max-height: calc(100vh - 300px);
}

.message-item {
  display: flex;
  margin-bottom: 24px;
  gap: 12px;
}

.user-message {
  flex-direction: row-reverse;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.user-message .message-content {
  text-align: right;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--text-secondary);
}

.user-message .message-header {
  flex-direction: row-reverse;
}

.message-text {
  background: var(--card-bg);
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid var(--border-color-light);
  line-height: 1.6;
}

.user-message .message-text {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
}

.message-actions {
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.user-message .message-actions {
  text-align: right;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.chat-input {
  border-top: 1px solid var(--border-color-light);
  padding-top: 16px;
}

.message-input {
  border-radius: 8px 0 0 8px;
}

.send-button {
  border-radius: 0 8px 8px 0;
  height: auto;
}

.input-tips {
  margin-top: 8px;
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
  .ai-chat {
    padding: 16px;
  }
  
  .chat-container {
    height: auto;
  }
  
  .agent-sidebar {
    margin-bottom: 16px;
  }
  
  .message-content {
    max-width: 85%;
  }
}
</style>

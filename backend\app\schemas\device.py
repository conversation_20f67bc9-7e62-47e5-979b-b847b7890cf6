"""
Device Pydantic schemas
"""
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, field_validator
from datetime import datetime

from ..models.device import DeviceType, DeviceStatus


class DeviceBase(BaseModel):
    """Base device schema"""
    name: str = Field(..., min_length=1, max_length=255)
    ip_address: str = Field(..., pattern=r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$|^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$')
    mac_address: Optional[str] = Field(None, pattern=r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$')
    device_type: DeviceType = DeviceType.OTHER
    vendor: Optional[str] = Field(None, max_length=100)
    model: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    
    # Location
    location: Optional[str] = Field(None, max_length=255)
    building: Optional[str] = Field(None, max_length=100)
    floor: Optional[str] = Field(None, max_length=50)
    room: Optional[str] = Field(None, max_length=50)
    
    # Network Configuration
    subnet: Optional[str] = Field(None, max_length=50)
    vlan_id: Optional[int] = Field(None, ge=1, le=4094)
    snmp_community: Optional[str] = Field("public", max_length=100)
    snmp_version: Optional[str] = Field("2c", max_length=10)
    snmp_port: Optional[int] = Field(161, ge=1, le=65535)


class DeviceCreate(DeviceBase):
    """Schema for creating device"""
    pass


class DeviceUpdate(BaseModel):
    """Schema for updating device"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    mac_address: Optional[str] = Field(None, pattern=r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$')
    device_type: Optional[DeviceType] = None
    vendor: Optional[str] = Field(None, max_length=100)
    model: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    
    # Location
    location: Optional[str] = Field(None, max_length=255)
    building: Optional[str] = Field(None, max_length=100)
    floor: Optional[str] = Field(None, max_length=50)
    room: Optional[str] = Field(None, max_length=50)
    
    # Network Configuration
    subnet: Optional[str] = Field(None, max_length=50)
    vlan_id: Optional[int] = Field(None, ge=1, le=4094)
    snmp_community: Optional[str] = Field(None, max_length=100)
    snmp_version: Optional[str] = Field(None, max_length=10)
    snmp_port: Optional[int] = Field(None, ge=1, le=65535)
    
    # Status
    status: Optional[DeviceStatus] = None
    cpu_usage: Optional[float] = Field(None, ge=0, le=100)
    memory_usage: Optional[float] = Field(None, ge=0, le=100)
    disk_usage: Optional[float] = Field(None, ge=0, le=100)


class DeviceResponse(DeviceBase):
    """Schema for device response"""
    id: int
    status: DeviceStatus
    last_seen: Optional[str] = None
    uptime: Optional[int] = None
    cpu_usage: Optional[float] = None
    memory_usage: Optional[float] = None
    disk_usage: Optional[float] = None
    
    # Additional data
    system_info: Optional[Dict[str, Any]] = None
    interfaces: Optional[Dict[str, Any]] = None
    custom_fields: Optional[Dict[str, Any]] = None
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    is_active: bool
    
    # Computed properties
    health_score: Optional[float] = None
    
    class Config:
        from_attributes = True
        
    @field_validator('health_score', mode='before')
    @classmethod
    def calculate_health_score(cls, v, info):
        """Calculate health score based on status and resource usage"""
        if info.data is None:
            return v
        status = info.data.get('status')
        if status == DeviceStatus.OFFLINE:
            return 0.0
        elif status == DeviceStatus.CRITICAL:
            return 25.0
        elif status == DeviceStatus.WARNING:
            return 50.0
        elif status == DeviceStatus.ONLINE:
            cpu = info.data.get('cpu_usage', 0) or 0
            memory = info.data.get('memory_usage', 0) or 0
            disk = info.data.get('disk_usage', 0) or 0
            cpu_score = 100 - cpu
            memory_score = 100 - memory
            disk_score = 100 - disk
            return (cpu_score + memory_score + disk_score) / 3
        else:
            return 75.0


class DeviceStats(BaseModel):
    """Device statistics schema"""
    total_devices: int
    online_devices: int
    offline_devices: int
    warning_devices: int
    critical_devices: int
    device_types: Dict[str, int]
    average_health_score: float

"""
Monitoring and scheduling service
"""
import asyncio
from typing import Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from loguru import logger

from ..core.database import SessionLocal
from ..models.device import Device
from .device_service import DeviceService
from .alert_service import AlertService


class MonitoringService:
    """Background monitoring and scheduling service"""
    
    def __init__(self):
        self.device_service = DeviceService()
        self.alert_service = AlertService()
        self.is_running = False
        self.monitoring_tasks = {}
    
    async def start_monitoring(self):
        """Start background monitoring tasks"""
        if self.is_running:
            logger.warning("Monitoring service is already running")
            return
        
        self.is_running = True
        logger.info("Starting monitoring service...")
        
        # Start monitoring tasks
        self.monitoring_tasks = {
            'device_monitoring': asyncio.create_task(self._device_monitoring_loop()),
            'alert_escalation': asyncio.create_task(self._alert_escalation_loop()),
            'cleanup': asyncio.create_task(self._cleanup_loop())
        }
        
        logger.info("Monitoring service started successfully")
    
    async def stop_monitoring(self):
        """Stop background monitoring tasks"""
        if not self.is_running:
            logger.warning("Monitoring service is not running")
            return
        
        self.is_running = False
        logger.info("Stopping monitoring service...")
        
        # Cancel all monitoring tasks
        for task_name, task in self.monitoring_tasks.items():
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    logger.info(f"Cancelled monitoring task: {task_name}")
        
        self.monitoring_tasks.clear()
        logger.info("Monitoring service stopped")
    
    async def _device_monitoring_loop(self):
        """Main device monitoring loop"""
        logger.info("Starting device monitoring loop")
        
        while self.is_running:
            try:
                # Monitor all devices
                monitoring_result = await self.device_service.monitor_all_devices()
                
                logger.info(
                    f"Device monitoring cycle completed: "
                    f"{monitoring_result.get('devices_monitored', 0)} devices, "
                    f"{monitoring_result.get('total_metrics_collected', 0)} metrics, "
                    f"{monitoring_result.get('total_alerts_generated', 0)} alerts"
                )
                
                # Wait for next monitoring cycle (5 minutes)
                await asyncio.sleep(300)
                
            except asyncio.CancelledError:
                logger.info("Device monitoring loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in device monitoring loop: {e}")
                # Wait before retrying
                await asyncio.sleep(60)
    
    async def _alert_escalation_loop(self):
        """Alert escalation loop"""
        logger.info("Starting alert escalation loop")
        
        while self.is_running:
            try:
                with SessionLocal() as db:
                    await self.alert_service.escalate_alerts(db)
                
                # Run escalation every 30 minutes
                await asyncio.sleep(1800)
                
            except asyncio.CancelledError:
                logger.info("Alert escalation loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in alert escalation loop: {e}")
                # Wait before retrying
                await asyncio.sleep(300)
    
    async def _cleanup_loop(self):
        """Cleanup old data loop"""
        logger.info("Starting cleanup loop")
        
        while self.is_running:
            try:
                with SessionLocal() as db:
                    # Cleanup old alerts (30 days)
                    await self.alert_service.cleanup_old_alerts(db, days=30)
                    
                    # TODO: Add cleanup for old performance metrics
                    # await self._cleanup_old_metrics(db, days=90)
                
                # Run cleanup daily
                await asyncio.sleep(86400)
                
            except asyncio.CancelledError:
                logger.info("Cleanup loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                # Wait before retrying
                await asyncio.sleep(3600)
    
    async def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring service status"""
        try:
            task_status = {}
            for task_name, task in self.monitoring_tasks.items():
                if task.done():
                    if task.cancelled():
                        status = "cancelled"
                    elif task.exception():
                        status = f"failed: {task.exception()}"
                    else:
                        status = "completed"
                else:
                    status = "running"
                
                task_status[task_name] = status
            
            # Get recent monitoring statistics
            with SessionLocal() as db:
                total_devices = db.query(Device).filter(Device.is_active == True).count()
                
                # Get devices by status
                from ..models.device import DeviceStatus
                online_devices = db.query(Device).filter(
                    Device.is_active == True,
                    Device.status == DeviceStatus.ONLINE
                ).count()
                
                offline_devices = db.query(Device).filter(
                    Device.is_active == True,
                    Device.status == DeviceStatus.OFFLINE
                ).count()
                
                # Get recent alerts
                from ..models.alert import Alert, AlertStatus
                end_time = datetime.now()
                start_time = end_time - timedelta(hours=24)
                
                recent_alerts = db.query(Alert).filter(
                    Alert.created_at >= start_time,
                    Alert.is_active == True
                ).count()
                
                active_alerts = db.query(Alert).filter(
                    Alert.status.in_([AlertStatus.OPEN, AlertStatus.ACKNOWLEDGED]),
                    Alert.is_active == True
                ).count()
            
            return {
                "service_status": "running" if self.is_running else "stopped",
                "task_status": task_status,
                "device_statistics": {
                    "total_devices": total_devices,
                    "online_devices": online_devices,
                    "offline_devices": offline_devices
                },
                "alert_statistics": {
                    "recent_alerts_24h": recent_alerts,
                    "active_alerts": active_alerts
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting monitoring status: {e}")
            return {
                "service_status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def trigger_device_discovery(self, ip_range: str) -> Dict[str, Any]:
        """Trigger device discovery for IP range"""
        try:
            logger.info(f"Starting device discovery for range: {ip_range}")
            
            discovered_devices = await self.device_service.discover_devices(ip_range)
            
            # Auto-add discovered devices to database
            added_devices = []
            with SessionLocal() as db:
                for device_info in discovered_devices:
                    if device_info.get('responsive'):
                        # Check if device already exists
                        existing = db.query(Device).filter(
                            Device.ip_address == device_info['ip_address']
                        ).first()
                        
                        if not existing:
                            # Create new device
                            system_info = device_info.get('system_info', {})
                            device_name = system_info.get('system_name', f"Device-{device_info['ip_address']}")
                            
                            new_device = Device(
                                name=device_name,
                                ip_address=device_info['ip_address'],
                                description=f"Auto-discovered device: {system_info.get('system_description', '')}",
                                system_info=system_info
                            )
                            
                            db.add(new_device)
                            added_devices.append({
                                'ip_address': device_info['ip_address'],
                                'name': device_name,
                                'system_info': system_info
                            })
                
                if added_devices:
                    db.commit()
                    logger.info(f"Added {len(added_devices)} new devices from discovery")
            
            return {
                "ip_range": ip_range,
                "discovered_devices": len(discovered_devices),
                "responsive_devices": len([d for d in discovered_devices if d.get('responsive')]),
                "added_devices": len(added_devices),
                "device_details": added_devices,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in device discovery: {e}")
            return {
                "ip_range": ip_range,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }


# Global monitoring service instance
monitoring_service = MonitoringService()

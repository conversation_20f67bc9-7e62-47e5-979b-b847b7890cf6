"""
AI conversation and analysis models
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, Integer, Text, JSON, Enum, Boolean
from sqlalchemy.dialects.mysql import LONGTEXT
import enum

from .base import BaseModel


class ConversationStatus(str, enum.Enum):
    """Conversation status"""
    ACTIVE = "active"
    COMPLETED = "completed"
    ARCHIVED = "archived"


class MessageRole(str, enum.Enum):
    """Message role in conversation"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"


class AnalysisType(str, enum.Enum):
    """AI analysis types"""
    SECURITY_ANALYSIS = "security_analysis"
    PERFORMANCE_ANALYSIS = "performance_analysis"
    NETWORK_ANALYSIS = "network_analysis"
    ANOMALY_DETECTION = "anomaly_detection"
    GENERAL_QUERY = "general_query"


class Conversation(BaseModel):
    """AI conversation model"""
    __tablename__ = "conversations"
    
    # Basic Information
    title = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    status = Column(Enum(ConversationStatus), nullable=False, default=ConversationStatus.ACTIVE)
    
    # User Information
    user_id = Column(String(100), nullable=True)  # User identifier
    user_name = Column(String(100), nullable=True)
    session_id = Column(String(100), nullable=True, index=True)
    
    # Conversation Metadata
    total_messages = Column(Integer, nullable=False, default=0)
    total_tokens = Column(Integer, nullable=True)
    analysis_type = Column(Enum(AnalysisType), nullable=True)
    
    # Timing
    started_at = Column(String(50), nullable=True)  # ISO string
    last_activity = Column(String(50), nullable=True)  # ISO string
    completed_at = Column(String(50), nullable=True)  # ISO string
    
    # Additional Data
    context = Column(JSON, nullable=True)  # Conversation context
    metadata = Column(JSON, nullable=True)  # Additional metadata
    tags = Column(JSON, nullable=True)  # Conversation tags
    
    def __repr__(self):
        return f"<Conversation(id={self.id}, title='{self.title}', status='{self.status}')>"


class Message(BaseModel):
    """Conversation message model"""
    __tablename__ = "messages"
    
    # Conversation Association
    conversation_id = Column(Integer, nullable=False, index=True)
    
    # Message Information
    role = Column(Enum(MessageRole), nullable=False)
    content = Column(LONGTEXT, nullable=False)  # Support long messages
    
    # Message Metadata
    token_count = Column(Integer, nullable=True)
    model_used = Column(String(100), nullable=True)  # AI model used
    
    # Timing
    timestamp = Column(String(50), nullable=False, index=True)  # ISO string
    processing_time = Column(Float, nullable=True)  # Seconds
    
    # Tool Usage
    tool_calls = Column(JSON, nullable=True)  # Tool calls made
    tool_results = Column(JSON, nullable=True)  # Tool results
    
    # Additional Data
    metadata = Column(JSON, nullable=True)  # Additional message data
    
    def __repr__(self):
        return f"<Message(conversation_id={self.conversation_id}, role='{self.role}', content_length={len(self.content or '')})>"


class AnalysisResult(BaseModel):
    """AI analysis result model"""
    __tablename__ = "analysis_results"
    
    # Analysis Information
    analysis_type = Column(Enum(AnalysisType), nullable=False)
    title = Column(String(255), nullable=False)
    summary = Column(Text, nullable=True)
    
    # Input Data
    input_data = Column(JSON, nullable=True)  # Input data for analysis
    query = Column(Text, nullable=True)  # Original query
    
    # Results
    findings = Column(JSON, nullable=True)  # Analysis findings
    recommendations = Column(JSON, nullable=True)  # Recommendations
    confidence_score = Column(Float, nullable=True)  # Confidence (0-1)
    
    # Processing Information
    model_used = Column(String(100), nullable=True)
    processing_time = Column(Float, nullable=True)  # Seconds
    token_usage = Column(JSON, nullable=True)  # Token usage details
    
    # Association
    conversation_id = Column(Integer, nullable=True, index=True)
    device_id = Column(Integer, nullable=True, index=True)
    
    # Timing
    analyzed_at = Column(String(50), nullable=False, index=True)  # ISO string
    
    # Additional Data
    metadata = Column(JSON, nullable=True)
    
    def __repr__(self):
        return f"<AnalysisResult(type='{self.analysis_type}', title='{self.title}', confidence={self.confidence_score})>"

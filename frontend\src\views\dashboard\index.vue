<template>
  <div class="dashboard">
    <!-- Header Stats -->
    <a-row :gutter="[16, 16]" class="stats-row">
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card">
          <a-statistic
            title="设备总数"
            :value="deviceStats?.total_devices || 0"
            :prefix="h(DesktopOutlined)"
            :value-style="{ color: '#1890ff' }"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card">
          <a-statistic
            title="在线设备"
            :value="deviceStats?.online_devices || 0"
            :prefix="h(CheckCircleOutlined)"
            :value-style="{ color: '#52c41a' }"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card">
          <a-statistic
            title="活跃告警"
            :value="alertStats?.open_alerts || 0"
            :prefix="h(AlertOutlined)"
            :value-style="{ color: '#ff4d4f' }"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card">
          <a-statistic
            title="健康度"
            :value="deviceStats?.average_health_score || 0"
            suffix="%"
            :prefix="h(HeartOutlined)"
            :value-style="{ color: getHealthColor(deviceStats?.average_health_score) }"
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- Charts Row -->
    <a-row :gutter="[16, 16]" class="charts-row">
      <!-- Device Status Chart -->
      <a-col :xs="24" :lg="12">
        <a-card title="设备状态分布" class="chart-card">
          <div ref="deviceStatusChart" class="chart-container"></div>
        </a-card>
      </a-col>
      
      <!-- Alert Trend Chart -->
      <a-col :xs="24" :lg="12">
        <a-card title="告警趋势" class="chart-card">
          <div ref="alertTrendChart" class="chart-container"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- Device List and Recent Alerts -->
    <a-row :gutter="[16, 16]" class="content-row">
      <!-- Recent Devices -->
      <a-col :xs="24" :lg="12">
        <a-card title="设备列表" class="content-card">
          <template #extra>
            <a-button type="link" @click="$router.push('/devices')">
              查看全部
            </a-button>
          </template>
          
          <a-list
            :data-source="recentDevices"
            :loading="deviceStore.loading"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-badge 
                      :status="getDeviceStatusBadge(item.status)" 
                      :text="item.name"
                    />
                  </template>
                  <template #description>
                    <div class="device-info">
                      <span>{{ item.ip_address }}</span>
                      <span>{{ item.device_type }}</span>
                      <span v-if="item.cpu_usage">CPU: {{ item.cpu_usage }}%</span>
                    </div>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a-button 
                    type="link" 
                    size="small"
                    @click="$router.push(`/devices/${item.id}`)"
                  >
                    详情
                  </a-button>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
      
      <!-- Recent Alerts -->
      <a-col :xs="24" :lg="12">
        <a-card title="最新告警" class="content-card">
          <template #extra>
            <a-button type="link" @click="$router.push('/alerts')">
              查看全部
            </a-button>
          </template>
          
          <a-list
            :data-source="recentAlerts"
            :loading="alertStore.loading"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-badge 
                      :status="getAlertStatusBadge(item.severity)" 
                      :text="item.title"
                    />
                  </template>
                  <template #description>
                    <div class="alert-info">
                      <span>{{ item.device_ip || '系统' }}</span>
                      <span>{{ formatTime(item.created_at) }}</span>
                    </div>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a-button 
                    type="link" 
                    size="small"
                    @click="handleAlertAction(item)"
                  >
                    {{ item.status === 'open' ? '确认' : '查看' }}
                  </a-button>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>

    <!-- System Status -->
    <a-row :gutter="[16, 16]" class="status-row">
      <a-col :span="24">
        <a-card title="系统状态" class="status-card">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="8">
              <div class="status-item">
                <div class="status-label">监控服务</div>
                <a-badge 
                  :status="wsStore.connected ? 'success' : 'error'" 
                  :text="wsStore.connected ? '运行中' : '已停止'"
                />
              </div>
            </a-col>
            
            <a-col :xs="24" :sm="8">
              <div class="status-item">
                <div class="status-label">AI服务</div>
                <a-badge 
                  :status="aiStatus ? 'success' : 'error'" 
                  :text="aiStatus ? '可用' : '不可用'"
                />
              </div>
            </a-col>
            
            <a-col :xs="24" :sm="8">
              <div class="status-item">
                <div class="status-label">数据库</div>
                <a-badge 
                  status="success" 
                  text="正常"
                />
              </div>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, h, nextTick } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { useAlertStore } from '@/stores/alert'
import { useWebSocketStore } from '@/stores/websocket'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  DesktopOutlined,
  CheckCircleOutlined,
  AlertOutlined,
  HeartOutlined
} from '@ant-design/icons-vue'

// Stores
const deviceStore = useDeviceStore()
const alertStore = useAlertStore()
const wsStore = useWebSocketStore()

// Refs
const deviceStatusChart = ref<HTMLElement>()
const alertTrendChart = ref<HTMLElement>()

// State
const aiStatus = ref(true) // TODO: Get from API
const deviceStats = computed(() => deviceStore.stats)
const alertStats = computed(() => alertStore.stats)
const recentDevices = computed(() => deviceStore.devices.slice(0, 8))
const recentAlerts = computed(() => alertStore.alerts.slice(0, 8))

// Methods
const getHealthColor = (score?: number) => {
  if (!score) return '#d9d9d9'
  if (score >= 80) return '#52c41a'
  if (score >= 60) return '#faad14'
  return '#ff4d4f'
}

const getDeviceStatusBadge = (status: string) => {
  const statusMap: Record<string, string> = {
    online: 'success',
    offline: 'error',
    warning: 'warning',
    critical: 'error'
  }
  return statusMap[status] || 'default'
}

const getAlertStatusBadge = (severity: string) => {
  const severityMap: Record<string, string> = {
    low: 'default',
    medium: 'warning',
    high: 'error',
    critical: 'error'
  }
  return severityMap[severity] || 'default'
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

const handleAlertAction = async (alert: any) => {
  if (alert.status === 'open') {
    try {
      await alertStore.acknowledgeAlert(alert.id)
    } catch (error) {
      console.error('Failed to acknowledge alert:', error)
    }
  }
}

const initDeviceStatusChart = () => {
  if (!deviceStatusChart.value || !deviceStats.value) return
  
  const chart = echarts.init(deviceStatusChart.value)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '设备状态',
        type: 'pie',
        radius: '50%',
        data: [
          { value: deviceStats.value.online_devices, name: '在线' },
          { value: deviceStats.value.offline_devices, name: '离线' },
          { value: deviceStats.value.warning_devices, name: '告警' },
          { value: deviceStats.value.critical_devices, name: '严重' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const initAlertTrendChart = () => {
  if (!alertTrendChart.value) return
  
  const chart = echarts.init(alertTrendChart.value)
  
  // Mock data for demo
  const hours = []
  const alertCounts = []
  
  for (let i = 23; i >= 0; i--) {
    hours.push(dayjs().subtract(i, 'hour').format('HH:mm'))
    alertCounts.push(Math.floor(Math.random() * 10))
  }
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: hours
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '告警数量',
        type: 'line',
        data: alertCounts,
        smooth: true,
        areaStyle: {}
      }
    ]
  }
  
  chart.setOption(option)
}

// Lifecycle
onMounted(async () => {
  // Fetch data
  await Promise.all([
    deviceStore.fetchDevices({ limit: 10 }),
    deviceStore.fetchStats(),
    alertStore.fetchAlerts({ limit: 10 }),
    alertStore.fetchStats()
  ])
  
  // Initialize charts
  await nextTick()
  initDeviceStatusChart()
  initAlertTrendChart()
})
</script>

<style scoped>
.dashboard {
  padding: 24px;
  background: var(--bg-color);
  min-height: 100vh;
}

.stats-row,
.charts-row,
.content-row,
.status-row {
  margin-bottom: 16px;
}

.stat-card,
.chart-card,
.content-card,
.status-card {
  height: 100%;
}

.chart-container {
  height: 300px;
}

.device-info,
.alert-info {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--text-secondary);
}

.status-item {
  text-align: center;
}

.status-label {
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-primary);
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }
  
  .chart-container {
    height: 250px;
  }
}
</style>

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message, notification } from 'ant-design-vue'
import { useDeviceStore } from './device'
import { useAlertStore } from './alert'

export interface WebSocketMessage {
  type: string
  payload: any
  timestamp: string
}

export interface AlertMessage extends WebSocketMessage {
  type: 'alert'
  payload: {
    id: number
    title: string
    message: string
    severity: 'low' | 'medium' | 'high' | 'critical'
    device_ip?: string
    device_name?: string
    alert_type: string
    created_at: string
  }
}

export interface DeviceStatusMessage extends WebSocketMessage {
  type: 'device_status'
  payload: {
    device_id: number
    ip_address: string
    name: string
    status: 'online' | 'offline' | 'warning' | 'critical'
    cpu_usage?: number
    memory_usage?: number
    last_seen: string
  }
}

export interface PerformanceMessage extends WebSocketMessage {
  type: 'performance'
  payload: {
    device_id: number
    device_ip: string
    metric_name: string
    metric_value: number
    metric_unit?: string
    timestamp: string
    threshold_exceeded?: boolean
  }
}

export interface ThreatMessage extends WebSocketMessage {
  type: 'threat'
  payload: {
    ip_address: string
    threat_level: string
    is_malicious: boolean
    reason: string
    source: string
    timestamp: string
  }
}

export const useWebSocketStore = defineStore('websocket', () => {
  // State
  const connected = ref(false)
  const connecting = ref(false)
  const error = ref<string | null>(null)
  const lastMessage = ref<WebSocketMessage | null>(null)
  const messages = ref<WebSocketMessage[]>([])
  
  // WebSocket instance
  let ws: WebSocket | null = null
  let reconnectTimer: NodeJS.Timeout | null = null
  let heartbeatTimer: NodeJS.Timeout | null = null
  
  // Config
  const maxReconnectAttempts = 5
  const reconnectInterval = 3000
  const heartbeatInterval = 30000
  let reconnectAttempts = 0

  // Getters
  const connectionStatus = computed(() => {
    if (connecting.value) return 'connecting'
    if (connected.value) return 'connected'
    if (error.value) return 'error'
    return 'disconnected'
  })

  // Actions
  const connect = (userId?: string) => {
    if (ws && (ws.readyState === WebSocket.CONNECTING || ws.readyState === WebSocket.OPEN)) {
      return
    }

    connecting.value = true
    error.value = null

    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000/api/v1/ws'
    const url = userId ? `${wsUrl}/connect?user_id=${userId}` : `${wsUrl}/connect`

    try {
      ws = new WebSocket(url)

      ws.onopen = () => {
        connected.value = true
        connecting.value = false
        reconnectAttempts = 0
        error.value = null
        
        console.log('WebSocket connected')
        
        // Start heartbeat
        startHeartbeat()
      }

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          lastMessage.value = message
          messages.value.push(message)
          
          // Keep only last 100 messages
          if (messages.value.length > 100) {
            messages.value = messages.value.slice(-100)
          }
          
          // Handle different message types
          handleMessage(message)
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err)
        }
      }

      ws.onclose = (event) => {
        connected.value = false
        connecting.value = false
        
        console.log('WebSocket disconnected:', event.code, event.reason)
        
        // Stop heartbeat
        stopHeartbeat()
        
        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
          scheduleReconnect()
        }
      }

      ws.onerror = (event) => {
        console.error('WebSocket error:', event)
        error.value = 'WebSocket连接错误'
        connecting.value = false
      }

    } catch (err: any) {
      connecting.value = false
      error.value = err.message || 'WebSocket连接失败'
      console.error('Failed to create WebSocket:', err)
    }
  }

  const disconnect = () => {
    if (ws) {
      ws.close(1000, 'User disconnected')
      ws = null
    }
    
    stopHeartbeat()
    clearReconnectTimer()
    
    connected.value = false
    connecting.value = false
    error.value = null
  }

  const send = (message: any) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message))
      return true
    }
    return false
  }

  const subscribe = (subscription: string) => {
    return send({
      type: 'subscribe',
      subscription
    })
  }

  const ping = () => {
    return send({
      type: 'ping',
      timestamp: new Date().toISOString()
    })
  }

  // Private methods
  const handleMessage = (message: WebSocketMessage) => {
    const deviceStore = useDeviceStore()
    const alertStore = useAlertStore()

    switch (message.type) {
      case 'alert':
        handleAlertMessage(message as AlertMessage)
        break

      case 'device_status':
        handleDeviceStatusMessage(message as DeviceStatusMessage)
        break

      case 'performance':
        handlePerformanceMessage(message as PerformanceMessage)
        break

      case 'threat':
        handleThreatMessage(message as ThreatMessage)
        break

      case 'device_update':
        // Update device in store (legacy support)
        if (message.payload?.id) {
          const device = deviceStore.devices.find(d => d.id === message.payload.id)
          if (device) {
            Object.assign(device, message.payload)
          }
        }
        break

      case 'new_alert':
        // Add new alert to store (legacy support)
        alertStore.addAlert(message.payload)
        break

      case 'topology_update':
        // Handle topology update
        console.log('Topology update received:', message.payload)
        break

      case 'system_status':
        // Handle system status update
        console.log('System status update:', message.payload)
        break

      case 'ai_response':
        // Handle AI response
        console.log('AI response received:', message.payload)
        break

      case 'monitoring_update':
        // Handle monitoring service update
        console.log('Monitoring update:', message.payload)
        break

      case 'pong':
        // Handle pong response
        console.log('Pong received')
        break

      case 'connection':
        // Handle connection status
        console.log('Connection status:', message.payload)
        break

      case 'subscription':
        // Handle subscription confirmation
        console.log('Subscription confirmed:', message.payload)
        break

      case 'error':
        // Handle error message
        console.error('WebSocket error message:', message.payload)
        error.value = message.payload?.message || 'WebSocket错误'
        break

      default:
        console.log('Unknown message type:', message.type, message.payload)
    }
  }

  // 处理告警消息
  const handleAlertMessage = (data: AlertMessage) => {
    const { payload } = data
    const alertStore = useAlertStore()

    // 添加到告警store
    alertStore.addAlert(payload)

    const severityConfig = {
      critical: { color: '#ff4d4f', icon: '🚨' },
      high: { color: '#fa8c16', icon: '⚠️' },
      medium: { color: '#faad14', icon: '🟡' },
      low: { color: '#1890ff', icon: 'ℹ️' }
    }

    const config = severityConfig[payload.severity] || severityConfig.low

    // 显示通知
    notification.open({
      message: `${config.icon} ${payload.title}`,
      description: payload.message,
      style: { borderLeft: `4px solid ${config.color}` },
      duration: payload.severity === 'critical' ? 0 : 4.5,
      onClick: () => {
        window.open(`/alerts/${payload.id}`, '_blank')
      }
    })
  }

  // 处理设备状态消息
  const handleDeviceStatusMessage = (data: DeviceStatusMessage) => {
    const { payload } = data
    const deviceStore = useDeviceStore()

    // 更新设备状态
    const device = deviceStore.devices.find(d => d.id === payload.device_id)
    if (device) {
      device.status = payload.status
      device.cpu_usage = payload.cpu_usage
      device.memory_usage = payload.memory_usage
      device.last_seen = payload.last_seen
    }

    // 如果设备状态变为离线或严重，显示通知
    if (payload.status === 'offline' || payload.status === 'critical') {
      const statusText = payload.status === 'offline' ? '离线' : '严重告警'
      const icon = payload.status === 'offline' ? '🔴' : '🚨'

      notification.warning({
        message: `${icon} 设备状态变更`,
        description: `设备 ${payload.name} (${payload.ip_address}) 状态变为${statusText}`,
        duration: 4.5
      })
    }
  }

  // 处理性能消息
  const handlePerformanceMessage = (data: PerformanceMessage) => {
    const { payload } = data

    // 如果性能指标超过阈值，显示警告
    if (payload.threshold_exceeded) {
      notification.warning({
        message: '⚠️ 性能告警',
        description: `设备 ${payload.device_ip} 的 ${payload.metric_name} 达到 ${payload.metric_value}${payload.metric_unit || ''}`,
        duration: 4.5
      })
    }
  }

  // 处理威胁消息
  const handleThreatMessage = (data: ThreatMessage) => {
    const { payload } = data

    if (payload.is_malicious) {
      const levelConfig = {
        critical: { icon: '🚨', color: '#ff4d4f' },
        high: { icon: '⚠️', color: '#fa8c16' },
        medium: { icon: '🟡', color: '#faad14' },
        low: { icon: 'ℹ️', color: '#1890ff' }
      }

      const config = levelConfig[payload.threat_level as keyof typeof levelConfig] || levelConfig.low

      notification.error({
        message: `${config.icon} 威胁检测`,
        description: `发现恶意IP: ${payload.ip_address} - ${payload.reason}`,
        style: { borderLeft: `4px solid ${config.color}` },
        duration: 0,
        onClick: () => {
          window.open(`/security/ip-reputation?ip=${payload.ip_address}`, '_blank')
        }
      })
    }
  }

  const scheduleReconnect = () => {
    if (reconnectTimer) return

    reconnectAttempts++
    const delay = reconnectInterval * Math.pow(2, reconnectAttempts - 1) // Exponential backoff
    
    console.log(`Scheduling reconnect attempt ${reconnectAttempts} in ${delay}ms`)
    
    reconnectTimer = setTimeout(() => {
      reconnectTimer = null
      connect()
    }, delay)
  }

  const clearReconnectTimer = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
  }

  const startHeartbeat = () => {
    stopHeartbeat()
    heartbeatTimer = setInterval(() => {
      ping()
    }, heartbeatInterval)
  }

  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearMessages = () => {
    messages.value = []
    lastMessage.value = null
  }

  return {
    // State
    connected,
    connecting,
    error,
    lastMessage,
    messages,

    // Getters
    connectionStatus,

    // Actions
    connect,
    disconnect,
    send,
    subscribe,
    ping,
    clearError,
    clearMessages
  }
})

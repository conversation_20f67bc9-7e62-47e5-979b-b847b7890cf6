"""
SNMP data collection service
"""
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from pysnmp.hlapi import *
from pysnmp.error import PySnmpError
from loguru import logger

from ..core.config import settings
from ..models.device import Device, DeviceStatus
from ..models.performance import PerformanceMetric


class SNMPCollector:
    """SNMP data collector for network devices"""
    
    # Common SNMP OIDs
    OIDS = {
        'system_name': '*******.2.1.1.5.0',
        'system_descr': '*******.2.1.1.1.0',
        'system_uptime': '*******.2.1.1.3.0',
        'system_contact': '*******.2.1.1.4.0',
        'system_location': '*******.2.1.1.6.0',
        
        # CPU and Memory (varies by vendor)
        'cpu_usage': '*******.4.1.9.9.109.1.1.1.1.7.1',  # Cisco CPU
        'memory_used': '*******.4.1.9.9.48.1.1.1.5.1',   # Cisco Memory Used
        'memory_free': '*******.4.1.9.9.48.1.1.1.6.1',   # Cisco Memory Free
        
        # Interface statistics
        'if_number': '*******.2.1.2.1.0',
        'if_descr': '*******.*******.1.2',
        'if_type': '*******.*******.1.3',
        'if_mtu': '*******.*******.1.4',
        'if_speed': '*******.*******.1.5',
        'if_admin_status': '*******.*******.1.7',
        'if_oper_status': '*******.*******.1.8',
        'if_in_octets': '*******.*******.1.10',
        'if_out_octets': '*******.*******.1.16',
        'if_in_errors': '*******.*******.1.14',
        'if_out_errors': '*******.*******.1.20',
    }
    
    def __init__(self):
        self.community = settings.SNMP_COMMUNITY
        self.timeout = settings.SNMP_TIMEOUT
        self.retries = settings.SNMP_RETRIES
        self.version = settings.SNMP_VERSION
    
    async def get_snmp_data(self, ip_address: str, oid: str, community: str = None) -> Optional[Any]:
        """Get single SNMP value"""
        try:
            community = community or self.community
            
            # Determine SNMP version
            if self.version == "1":
                snmp_version = 0
            elif self.version == "2c":
                snmp_version = 1
            else:
                snmp_version = 1  # Default to v2c
            
            # Perform SNMP GET
            for (errorIndication, errorStatus, errorIndex, varBinds) in getCmd(
                SnmpEngine(),
                CommunityData(community, mpModel=snmp_version),
                UdpTransportTarget((ip_address, 161), timeout=self.timeout, retries=self.retries),
                ContextData(),
                ObjectType(ObjectIdentity(oid))
            ):
                if errorIndication:
                    logger.error(f"SNMP error indication for {ip_address}: {errorIndication}")
                    return None
                elif errorStatus:
                    logger.error(f"SNMP error status for {ip_address}: {errorStatus.prettyPrint()} at {errorIndex and varBinds[int(errorIndex) - 1][0] or '?'}")
                    return None
                else:
                    for varBind in varBinds:
                        return varBind[1].prettyPrint()
            
            return None
            
        except PySnmpError as e:
            logger.error(f"SNMP error for {ip_address}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting SNMP data from {ip_address}: {e}")
            return None
    
    async def get_bulk_snmp_data(self, ip_address: str, oids: List[str], community: str = None) -> Dict[str, Any]:
        """Get multiple SNMP values in one request"""
        try:
            community = community or self.community
            results = {}
            
            # Determine SNMP version
            if self.version == "1":
                snmp_version = 0
            elif self.version == "2c":
                snmp_version = 1
            else:
                snmp_version = 1  # Default to v2c
            
            # Create ObjectType list
            object_types = [ObjectType(ObjectIdentity(oid)) for oid in oids]
            
            # Perform SNMP GET
            for (errorIndication, errorStatus, errorIndex, varBinds) in getCmd(
                SnmpEngine(),
                CommunityData(community, mpModel=snmp_version),
                UdpTransportTarget((ip_address, 161), timeout=self.timeout, retries=self.retries),
                ContextData(),
                *object_types
            ):
                if errorIndication:
                    logger.error(f"SNMP bulk error indication for {ip_address}: {errorIndication}")
                    return {}
                elif errorStatus:
                    logger.error(f"SNMP bulk error status for {ip_address}: {errorStatus.prettyPrint()}")
                    return {}
                else:
                    for i, varBind in enumerate(varBinds):
                        if i < len(oids):
                            results[oids[i]] = varBind[1].prettyPrint()
            
            return results
            
        except PySnmpError as e:
            logger.error(f"SNMP bulk error for {ip_address}: {e}")
            return {}
        except Exception as e:
            logger.error(f"Unexpected error getting bulk SNMP data from {ip_address}: {e}")
            return {}
    
    async def collect_device_info(self, device: Device) -> Dict[str, Any]:
        """Collect basic device information via SNMP"""
        try:
            # Get basic system information
            basic_oids = [
                self.OIDS['system_name'],
                self.OIDS['system_descr'],
                self.OIDS['system_uptime'],
                self.OIDS['system_contact'],
                self.OIDS['system_location']
            ]
            
            basic_data = await self.get_bulk_snmp_data(
                device.ip_address, 
                basic_oids, 
                device.snmp_community
            )
            
            # Parse results
            system_info = {}
            if basic_data:
                system_info = {
                    'system_name': basic_data.get(self.OIDS['system_name'], ''),
                    'system_description': basic_data.get(self.OIDS['system_descr'], ''),
                    'system_uptime': basic_data.get(self.OIDS['system_uptime'], ''),
                    'system_contact': basic_data.get(self.OIDS['system_contact'], ''),
                    'system_location': basic_data.get(self.OIDS['system_location'], ''),
                    'last_collected': datetime.now().isoformat()
                }
            
            return system_info
            
        except Exception as e:
            logger.error(f"Error collecting device info for {device.ip_address}: {e}")
            return {}
    
    async def collect_performance_metrics(self, device: Device) -> List[Dict[str, Any]]:
        """Collect performance metrics via SNMP"""
        try:
            metrics = []
            timestamp = datetime.now().isoformat()
            
            # Try to get CPU usage
            cpu_value = await self.get_snmp_data(
                device.ip_address, 
                self.OIDS['cpu_usage'], 
                device.snmp_community
            )
            
            if cpu_value and cpu_value.isdigit():
                metrics.append({
                    'device_id': device.id,
                    'device_ip': device.ip_address,
                    'metric_name': 'cpu_usage',
                    'metric_value': float(cpu_value),
                    'metric_unit': '%',
                    'timestamp': timestamp
                })
            
            # Try to get memory usage
            memory_used = await self.get_snmp_data(
                device.ip_address, 
                self.OIDS['memory_used'], 
                device.snmp_community
            )
            memory_free = await self.get_snmp_data(
                device.ip_address, 
                self.OIDS['memory_free'], 
                device.snmp_community
            )
            
            if memory_used and memory_free and memory_used.isdigit() and memory_free.isdigit():
                used = float(memory_used)
                free = float(memory_free)
                total = used + free
                if total > 0:
                    memory_percentage = (used / total) * 100
                    metrics.append({
                        'device_id': device.id,
                        'device_ip': device.ip_address,
                        'metric_name': 'memory_usage',
                        'metric_value': memory_percentage,
                        'metric_unit': '%',
                        'timestamp': timestamp
                    })
            
            # Get interface statistics
            interface_metrics = await self.collect_interface_metrics(device)
            metrics.extend(interface_metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting performance metrics for {device.ip_address}: {e}")
            return []
    
    async def collect_interface_metrics(self, device: Device) -> List[Dict[str, Any]]:
        """Collect interface statistics"""
        try:
            metrics = []
            timestamp = datetime.now().isoformat()
            
            # Get number of interfaces
            if_number = await self.get_snmp_data(
                device.ip_address, 
                self.OIDS['if_number'], 
                device.snmp_community
            )
            
            if not if_number or not if_number.isdigit():
                return metrics
            
            num_interfaces = int(if_number)
            
            # Collect data for each interface (limit to first 10 for performance)
            for i in range(1, min(num_interfaces + 1, 11)):
                # Get interface description
                if_descr = await self.get_snmp_data(
                    device.ip_address,
                    f"{self.OIDS['if_descr']}.{i}",
                    device.snmp_community
                )
                
                # Get interface operational status
                if_oper_status = await self.get_snmp_data(
                    device.ip_address,
                    f"{self.OIDS['if_oper_status']}.{i}",
                    device.snmp_community
                )
                
                # Get interface speed
                if_speed = await self.get_snmp_data(
                    device.ip_address,
                    f"{self.OIDS['if_speed']}.{i}",
                    device.snmp_community
                )
                
                # Get interface traffic
                if_in_octets = await self.get_snmp_data(
                    device.ip_address,
                    f"{self.OIDS['if_in_octets']}.{i}",
                    device.snmp_community
                )
                
                if_out_octets = await self.get_snmp_data(
                    device.ip_address,
                    f"{self.OIDS['if_out_octets']}.{i}",
                    device.snmp_community
                )
                
                # Create interface metric
                if if_descr:
                    interface_data = {
                        'device_id': device.id,
                        'device_ip': device.ip_address,
                        'metric_name': f'interface_{i}_status',
                        'metric_value': float(if_oper_status) if if_oper_status and if_oper_status.isdigit() else 0,
                        'metric_unit': 'status',
                        'timestamp': timestamp,
                        'metadata': {
                            'interface_index': i,
                            'interface_description': if_descr,
                            'interface_speed': if_speed,
                            'in_octets': if_in_octets,
                            'out_octets': if_out_octets
                        }
                    }
                    metrics.append(interface_data)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting interface metrics for {device.ip_address}: {e}")
            return []
    
    async def test_connectivity(self, ip_address: str, community: str = None) -> bool:
        """Test SNMP connectivity to device"""
        try:
            result = await self.get_snmp_data(ip_address, self.OIDS['system_name'], community)
            return result is not None
        except Exception as e:
            logger.error(f"SNMP connectivity test failed for {ip_address}: {e}")
            return False

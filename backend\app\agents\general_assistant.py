"""
General Assistant Agent - 通用网络监控助手
"""
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from loguru import logger

from agents import Agent, Runner, function_tool, set_tracing_disabled
from agents.extensions.models.litellm_model import LitellmModel

from ..core.config import settings
from ..core.database import SessionLocal
from ..models.device import Device, DeviceStatus
from ..models.alert import Alert, AlertStatus, AlertSeverity
from ..models.performance import PerformanceMetric

# 禁用追踪功能（因为不使用OpenAI模型）
set_tracing_disabled(True)


@function_tool
async def get_device_status(device_id: Optional[int] = None, device_ip: Optional[str] = None) -> str:
    """获取设备状态信息
    
    Args:
        device_id: 设备ID（可选）
        device_ip: 设备IP地址（可选）
    
    Returns:
        设备状态信息字符串
    """
    try:
        with SessionLocal() as db:
            query = db.query(Device).filter(Device.is_active == True)
            
            if device_id:
                device = query.filter(Device.id == device_id).first()
                if device:
                    return f"设备 {device.name} ({device.ip_address}) 状态：{device.status.value}，CPU使用率：{device.cpu_usage or 0}%，内存使用率：{device.memory_usage or 0}%"
                else:
                    return f"未找到ID为{device_id}的设备"
            
            elif device_ip:
                device = query.filter(Device.ip_address == device_ip).first()
                if device:
                    return f"设备 {device.name} ({device.ip_address}) 状态：{device.status.value}，CPU使用率：{device.cpu_usage or 0}%，内存使用率：{device.memory_usage or 0}%"
                else:
                    return f"未找到IP为{device_ip}的设备"
            
            else:
                # 返回所有设备概览
                devices = query.all()
                online_count = sum(1 for d in devices if d.status == DeviceStatus.ONLINE)
                offline_count = sum(1 for d in devices if d.status == DeviceStatus.OFFLINE)
                warning_count = sum(1 for d in devices if d.status == DeviceStatus.WARNING)
                critical_count = sum(1 for d in devices if d.status == DeviceStatus.CRITICAL)
                
                return f"设备状态概览：共{len(devices)}台设备，在线{online_count}台，离线{offline_count}台，告警{warning_count}台，严重{critical_count}台"
                
    except Exception as e:
        logger.error(f"获取设备状态失败: {e}")
        return f"获取设备状态失败：{str(e)}"


@function_tool
async def get_alert_summary(severity: Optional[str] = None, hours: int = 24) -> str:
    """获取告警概览信息
    
    Args:
        severity: 告警级别过滤（可选）
        hours: 查询最近多少小时的告警（默认24小时）
    
    Returns:
        告警概览信息字符串
    """
    try:
        with SessionLocal() as db:
            # 计算时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            query = db.query(Alert).filter(
                Alert.is_active == True,
                Alert.created_at >= start_time
            )
            
            if severity:
                try:
                    severity_enum = AlertSeverity(severity.lower())
                    query = query.filter(Alert.severity == severity_enum)
                except ValueError:
                    return f"无效的告警级别：{severity}，有效值为：low, medium, high, critical"
            
            alerts = query.all()
            
            if not alerts:
                return f"最近{hours}小时内没有告警"
            
            # 按级别统计
            critical_count = sum(1 for a in alerts if a.severity == AlertSeverity.CRITICAL)
            high_count = sum(1 for a in alerts if a.severity == AlertSeverity.HIGH)
            medium_count = sum(1 for a in alerts if a.severity == AlertSeverity.MEDIUM)
            low_count = sum(1 for a in alerts if a.severity == AlertSeverity.LOW)
            
            # 按状态统计
            open_count = sum(1 for a in alerts if a.status == AlertStatus.OPEN)
            ack_count = sum(1 for a in alerts if a.status == AlertStatus.ACKNOWLEDGED)
            resolved_count = sum(1 for a in alerts if a.status == AlertStatus.RESOLVED)
            
            result = f"最近{hours}小时告警概览：\n"
            result += f"总计：{len(alerts)}个告警\n"
            result += f"按级别：严重{critical_count}个，高级{high_count}个，中级{medium_count}个，低级{low_count}个\n"
            result += f"按状态：未处理{open_count}个，已确认{ack_count}个，已解决{resolved_count}个"
            
            return result
            
    except Exception as e:
        logger.error(f"获取告警概览失败: {e}")
        return f"获取告警概览失败：{str(e)}"


@function_tool
async def get_performance_metrics(device_id: Optional[int] = None, metric_name: Optional[str] = None, hours: int = 1) -> str:
    """获取性能指标信息
    
    Args:
        device_id: 设备ID（可选）
        metric_name: 指标名称（可选）
        hours: 查询最近多少小时的数据（默认1小时）
    
    Returns:
        性能指标信息字符串
    """
    try:
        from sqlalchemy import desc
        from datetime import timedelta
        
        with SessionLocal() as db:
            # 计算时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            query = db.query(PerformanceMetric).filter(
                PerformanceMetric.is_active == True,
                PerformanceMetric.timestamp >= start_time
            )
            
            if device_id:
                query = query.filter(PerformanceMetric.device_id == device_id)
            
            if metric_name:
                query = query.filter(PerformanceMetric.metric_name.ilike(f"%{metric_name}%"))
            
            metrics = query.order_by(desc(PerformanceMetric.timestamp)).limit(20).all()
            
            if not metrics:
                return f"最近{hours}小时内未找到性能指标数据"
            
            result = f"最近{hours}小时性能指标（最新20条）：\n"
            for metric in metrics:
                device_info = f"设备ID:{metric.device_id}" if metric.device_id else f"IP:{metric.device_ip}"
                result += f"- {metric.metric_name}: {metric.metric_value}{metric.metric_unit or ''} ({device_info}, {metric.timestamp.strftime('%H:%M:%S')})\n"
            
            return result
            
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        return f"获取性能指标失败：{str(e)}"


@function_tool
async def get_network_topology_info() -> str:
    """获取网络拓扑信息
    
    Returns:
        网络拓扑概览信息
    """
    try:
        with SessionLocal() as db:
            # 获取设备统计
            devices = db.query(Device).filter(Device.is_active == True).all()
            
            # 按设备类型统计
            device_types = {}
            for device in devices:
                device_type = device.device_type.value if device.device_type else "unknown"
                device_types[device_type] = device_types.get(device_type, 0) + 1
            
            # 按状态统计
            status_counts = {}
            for device in devices:
                status = device.status.value if device.status else "unknown"
                status_counts[status] = status_counts.get(status, 0) + 1
            
            result = f"网络拓扑概览：\n"
            result += f"总设备数：{len(devices)}\n"
            result += f"设备类型分布：{', '.join([f'{k}:{v}台' for k, v in device_types.items()])}\n"
            result += f"设备状态分布：{', '.join([f'{k}:{v}台' for k, v in status_counts.items()])}"
            
            return result
            
    except Exception as e:
        logger.error(f"获取网络拓扑信息失败: {e}")
        return f"获取网络拓扑信息失败：{str(e)}"


class GeneralAssistantAgent:
    """通用网络监控助手Agent"""
    
    def __init__(self):
        """初始化通用助手Agent"""
        try:
            # 创建LiteLLM模型实例
            self.model = LitellmModel(
                model=f"deepseek/{settings.DEEPSEEK_MODEL}",
                api_key=settings.DEEPSEEK_API_KEY,
                base_url=settings.DEEPSEEK_BASE_URL
            )
            
            # 创建Agent实例
            self.agent = Agent(
                name="CampusGuard通用助手",
                instructions="""你是CampusGuard网络监控系统的AI助手。你的主要职责是：

1. 回答用户关于网络监控的问题
2. 提供设备状态信息和告警概览
3. 解释性能指标和网络拓扑
4. 协助用户理解网络问题
5. 提供网络管理建议

请用中文回复，保持专业、友好和有帮助的语调。当用户询问具体的设备状态、告警信息或性能指标时，使用相应的工具函数获取实时数据。

如果用户的问题涉及安全分析，请建议他们咨询安全专家。
如果用户的问题涉及性能分析，请建议他们咨询性能专家。""",
                model=self.model,
                tools=[
                    get_device_status,
                    get_alert_summary,
                    get_performance_metrics,
                    get_network_topology_info
                ]
            )
            
            logger.info("通用助手Agent初始化成功")
            
        except Exception as e:
            logger.error(f"通用助手Agent初始化失败: {e}")
            raise
    
    async def chat(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """与通用助手对话
        
        Args:
            message: 用户消息
            context: 上下文信息（可选）
        
        Returns:
            包含响应信息的字典
        """
        try:
            logger.info(f"通用助手收到消息: {message}")
            
            # 运行Agent
            result = await Runner.run(
                self.agent,
                input=message,
                max_turns=5
            )
            
            response_data = {
                "agent_name": "通用助手",
                "agent_type": "general_assistant",
                "response": result.final_output,
                "timestamp": datetime.now().isoformat(),
                "success": True
            }
            
            logger.info("通用助手响应成功")
            return response_data
            
        except Exception as e:
            logger.error(f"通用助手对话失败: {e}")
            return {
                "agent_name": "通用助手",
                "agent_type": "general_assistant",
                "response": f"抱歉，处理您的请求时发生错误：{str(e)}",
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": str(e)
            }


# 全局通用助手实例
general_assistant = GeneralAssistantAgent()

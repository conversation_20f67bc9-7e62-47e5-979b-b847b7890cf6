<template>
  <div class="threat-intel-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>威胁情报仪表盘</h1>
      <a-button type="primary" @click="refreshData" :loading="loading">
        <template #icon><ReloadOutlined /></template>
        刷新数据
      </a-button>
    </div>

    <!-- 统计卡片 -->
    <a-row :gutter="16" class="stats-cards">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="威胁情报总数"
            :value="statistics.total_threats"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix><SecurityScanOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="高危威胁"
            :value="statistics.high_severity_threats"
            :value-style="{ color: '#ff4d4f' }"
          >
            <template #prefix><ExclamationCircleOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="黑名单条目"
            :value="statistics.blacklist_entries"
            :value-style="{ color: '#fa8c16' }"
          >
            <template #prefix><StopOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="内置黑名单"
            :value="statistics.builtin_blacklist_count"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix><SafetyCertificateOutlined /></template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 威胁级别分布图表 -->
    <a-row :gutter="16" class="charts-section">
      <a-col :span="12">
        <a-card title="威胁级别分布" :loading="chartLoading">
          <div ref="threatLevelChart" style="height: 300px;"></div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="威胁来源分布" :loading="chartLoading">
          <div ref="threatSourceChart" style="height: 300px;"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 最新威胁事件 -->
    <a-card title="最新威胁情报" class="recent-threats">
      <template #extra>
        <a-button @click="updateThreatIntelligence" :loading="updateLoading">
          更新威胁情报
        </a-button>
      </template>
      
      <a-table
        :columns="threatColumns"
        :data-source="recentThreats"
        :loading="tableLoading"
        :pagination="{ pageSize: 10 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'threat_level'">
            <a-tag :color="getThreatLevelColor(record.threat_level)">
              {{ getThreatLevelLabel(record.threat_level) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'is_malicious'">
            <a-tag :color="record.is_malicious ? 'red' : 'green'">
              {{ record.is_malicious ? '恶意' : '安全' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'confidence_score'">
            <a-progress
              :percent="Math.round(record.confidence_score * 100)"
              size="small"
              :stroke-color="getConfidenceColor(record.confidence_score)"
            />
          </template>
          <template v-else-if="column.key === 'categories'">
            <a-tag v-for="category in record.categories" :key="category" size="small">
              {{ category }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'last_seen'">
            {{ formatTime(record.last_seen) }}
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 快速操作区域 -->
    <a-card title="快速操作" class="quick-actions">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-card size="small" hoverable @click="$router.push('/security/ip-reputation')">
            <template #cover>
              <div class="action-icon">
                <SearchOutlined style="font-size: 32px; color: #1890ff;" />
              </div>
            </template>
            <a-card-meta title="IP信誉查询" description="检查IP地址的安全状态和威胁级别" />
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card size="small" hoverable @click="$router.push('/security/blacklist')">
            <template #cover>
              <div class="action-icon">
                <StopOutlined style="font-size: 32px; color: #ff4d4f;" />
              </div>
            </template>
            <a-card-meta title="黑名单管理" description="管理IP黑名单，添加或移除威胁IP" />
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card size="small" hoverable @click="exportThreatData">
            <template #cover>
              <div class="action-icon">
                <DownloadOutlined style="font-size: 32px; color: #52c41a;" />
              </div>
            </template>
            <a-card-meta title="导出数据" description="导出威胁情报数据用于分析" />
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  SecurityScanOutlined,
  ExclamationCircleOutlined,
  StopOutlined,
  SafetyCertificateOutlined,
  SearchOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import { threatApi, threatUtils, type ThreatStatistics, type ThreatIntelligenceItem } from '@/api/threat'

// 响应式数据
const loading = ref(false)
const chartLoading = ref(false)
const tableLoading = ref(false)
const updateLoading = ref(false)

const statistics = ref<ThreatStatistics>({
  total_threats: 0,
  high_severity_threats: 0,
  blacklist_entries: 0,
  builtin_blacklist_count: 0
})

const recentThreats = ref<ThreatIntelligenceItem[]>([])

// 图表引用
const threatLevelChart = ref<HTMLElement>()
const threatSourceChart = ref<HTMLElement>()

// 表格列定义
const threatColumns = [
  { title: 'IP地址', dataIndex: 'ip_address', key: 'ip_address', width: 120 },
  { title: '威胁级别', dataIndex: 'threat_level', key: 'threat_level', width: 100 },
  { title: '恶意状态', dataIndex: 'is_malicious', key: 'is_malicious', width: 80 },
  { title: '置信度', dataIndex: 'confidence_score', key: 'confidence_score', width: 120 },
  { title: '威胁类别', dataIndex: 'categories', key: 'categories', width: 150 },
  { title: '来源', dataIndex: 'source', key: 'source', width: 100 },
  { title: '最后发现', dataIndex: 'last_seen', key: 'last_seen', width: 150 }
]

// 工具函数
const getThreatLevelColor = (level: string) => {
  const info = threatUtils.getThreatLevelInfo(level)
  return info.color
}

const getThreatLevelLabel = (level: string) => {
  const info = threatUtils.getThreatLevelInfo(level)
  return info.label
}

const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.8) return '#52c41a'
  if (confidence >= 0.6) return '#faad14'
  return '#ff4d4f'
}

const formatTime = (timestamp?: string) => {
  return threatUtils.formatTimestamp(timestamp)
}

// 数据加载函数
const loadStatistics = async () => {
  try {
    const response = await threatApi.getThreatStatistics()
    statistics.value = response.data
  } catch (error) {
    console.error('加载威胁统计失败:', error)
    message.error('加载威胁统计失败')
  }
}

const loadRecentThreats = async () => {
  try {
    tableLoading.value = true
    const response = await threatApi.getThreatIntelligence({ limit: 20 })
    recentThreats.value = response.data.items
  } catch (error) {
    console.error('加载威胁情报失败:', error)
    message.error('加载威胁情报失败')
  } finally {
    tableLoading.value = false
  }
}

// 图表初始化
const initCharts = async () => {
  await nextTick()
  chartLoading.value = true
  
  try {
    // 威胁级别分布图
    if (threatLevelChart.value) {
      const chart1 = echarts.init(threatLevelChart.value)
      const option1 = {
        tooltip: { trigger: 'item' },
        legend: { orient: 'vertical', left: 'left' },
        series: [{
          name: '威胁级别',
          type: 'pie',
          radius: '50%',
          data: [
            { value: statistics.value.high_severity_threats, name: '高危' },
            { value: Math.max(0, statistics.value.total_threats - statistics.value.high_severity_threats), name: '其他' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      chart1.setOption(option1)
    }

    // 威胁来源分布图
    if (threatSourceChart.value) {
      const chart2 = echarts.init(threatSourceChart.value)
      const option2 = {
        tooltip: { trigger: 'axis' },
        xAxis: { type: 'category', data: ['内置黑名单', '威胁情报', '手动添加'] },
        yAxis: { type: 'value' },
        series: [{
          data: [
            statistics.value.builtin_blacklist_count,
            statistics.value.total_threats,
            Math.max(0, statistics.value.blacklist_entries - statistics.value.builtin_blacklist_count)
          ],
          type: 'bar',
          itemStyle: { color: '#1890ff' }
        }]
      }
      chart2.setOption(option2)
    }
  } catch (error) {
    console.error('初始化图表失败:', error)
  } finally {
    chartLoading.value = false
  }
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadStatistics(),
      loadRecentThreats()
    ])
    await initCharts()
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

// 更新威胁情报
const updateThreatIntelligence = async () => {
  try {
    updateLoading.value = true
    await threatApi.updateThreatIntelligence()
    message.success('威胁情报更新成功')
    await refreshData()
  } catch (error) {
    console.error('更新威胁情报失败:', error)
    message.error('更新威胁情报失败')
  } finally {
    updateLoading.value = false
  }
}

// 导出威胁数据
const exportThreatData = () => {
  message.info('导出功能开发中...')
}

// 组件挂载
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.threat-intel-dashboard {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.stats-cards {
  margin-bottom: 24px;
}

.charts-section {
  margin-bottom: 24px;
}

.recent-threats {
  margin-bottom: 24px;
}

.quick-actions .action-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  background: #f5f5f5;
}

.quick-actions .ant-card-small .ant-card-body {
  padding: 12px;
}
</style>

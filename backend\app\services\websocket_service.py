"""
WebSocket service for real-time data broadcasting
"""
import json
import asyncio
from typing import Dict, Any, List
from datetime import datetime
from loguru import logger

from ..api.v1.endpoints.websocket import manager


class WebSocketService:
    """Service for managing WebSocket real-time data broadcasting"""
    
    def __init__(self):
        self.broadcast_queue = asyncio.Queue()
        self.is_broadcasting = False
    
    async def start_broadcasting(self):
        """Start the broadcasting service"""
        if self.is_broadcasting:
            logger.warning("WebSocket broadcasting is already running")
            return
        
        self.is_broadcasting = True
        logger.info("Starting WebSocket broadcasting service")
        
        # Start the broadcasting loop
        asyncio.create_task(self._broadcasting_loop())
    
    async def stop_broadcasting(self):
        """Stop the broadcasting service"""
        self.is_broadcasting = False
        logger.info("WebSocket broadcasting service stopped")
    
    async def _broadcasting_loop(self):
        """Main broadcasting loop"""
        while self.is_broadcasting:
            try:
                # Wait for messages to broadcast
                message = await asyncio.wait_for(self.broadcast_queue.get(), timeout=1.0)
                
                # Broadcast the message
                await manager.broadcast(json.dumps(message))
                
                # Mark task as done
                self.broadcast_queue.task_done()
                
            except asyncio.TimeoutError:
                # No messages to broadcast, continue
                continue
            except Exception as e:
                logger.error(f"Error in broadcasting loop: {e}")
                await asyncio.sleep(1)
    
    async def broadcast_device_update(self, device_data: Dict[str, Any]):
        """Queue device update for broadcasting"""
        message = {
            "type": "device_update",
            "data": device_data,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_queue.put(message)
        logger.debug(f"Queued device update broadcast for device {device_data.get('id')}")
    
    async def broadcast_alert(self, alert_data: Dict[str, Any]):
        """Queue alert for broadcasting"""
        message = {
            "type": "new_alert",
            "data": alert_data,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_queue.put(message)
        logger.debug(f"Queued alert broadcast: {alert_data.get('title')}")
    
    async def broadcast_performance_metrics(self, metrics_data: List[Dict[str, Any]]):
        """Queue performance metrics for broadcasting"""
        message = {
            "type": "performance_update",
            "data": {
                "metrics": metrics_data,
                "count": len(metrics_data)
            },
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_queue.put(message)
        logger.debug(f"Queued performance metrics broadcast: {len(metrics_data)} metrics")
    
    async def broadcast_topology_update(self, topology_data: Dict[str, Any]):
        """Queue network topology update for broadcasting"""
        message = {
            "type": "topology_update",
            "data": topology_data,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_queue.put(message)
        logger.debug("Queued topology update broadcast")
    
    async def broadcast_system_status(self, status_data: Dict[str, Any]):
        """Queue system status update for broadcasting"""
        message = {
            "type": "system_status",
            "data": status_data,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_queue.put(message)
        logger.debug("Queued system status broadcast")
    
    async def send_ai_response(self, user_id: str, response_data: Dict[str, Any]):
        """Send AI response to specific user"""
        message = {
            "type": "ai_response",
            "data": response_data,
            "timestamp": datetime.now().isoformat()
        }
        
        # Send to specific user
        await manager.send_to_user(json.dumps(message), user_id)
        logger.debug(f"Sent AI response to user {user_id}")
    
    async def broadcast_monitoring_update(self, monitoring_data: Dict[str, Any]):
        """Queue monitoring service update for broadcasting"""
        message = {
            "type": "monitoring_update",
            "data": monitoring_data,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_queue.put(message)
        logger.debug("Queued monitoring update broadcast")
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get WebSocket connection statistics"""
        return {
            "active_connections": len(manager.active_connections),
            "user_connections": len(manager.user_connections),
            "queue_size": self.broadcast_queue.qsize(),
            "is_broadcasting": self.is_broadcasting,
            "timestamp": datetime.now().isoformat()
        }
    
    async def broadcast_custom_message(self, message_type: str, data: Dict[str, Any]):
        """Broadcast custom message"""
        message = {
            "type": message_type,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_queue.put(message)
        logger.debug(f"Queued custom message broadcast: {message_type}")


# Global WebSocket service instance
websocket_service = WebSocketService()

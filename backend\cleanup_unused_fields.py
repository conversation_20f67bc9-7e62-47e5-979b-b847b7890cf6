#!/usr/bin/env python3
"""
清理未使用的数据库字段脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger

def cleanup_device_model():
    """清理Device模型中未使用的字段"""
    device_model_path = project_root / "app" / "models" / "device.py"
    
    logger.info("清理Device模型中的未使用字段...")
    
    with open(device_model_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除备份相关字段
    lines_to_remove = [
        "last_backup_time = Column(DateTime, nullable=True, comment=\"最后备份时间\")",
        "backup_status = Column(String(50), nullable=True, comment=\"备份状态\")",
    ]
    
    for line in lines_to_remove:
        if line in content:
            content = content.replace(line + "\n", "")
            logger.info(f"移除字段: {line.split('=')[0].strip()}")
    
    with open(device_model_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info("Device模型清理完成")

def cleanup_alert_model():
    """清理Alert模型中未使用的字段"""
    alert_model_path = project_root / "app" / "models" / "alert.py"
    
    logger.info("清理Alert模型中的未使用字段...")
    
    with open(alert_model_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除升级相关字段
    lines_to_remove = [
        "escalation_level = Column(Integer, default=0, comment=\"升级级别\")",
    ]
    
    for line in lines_to_remove:
        if line in content:
            content = content.replace(line + "\n", "")
            logger.info(f"移除字段: {line.split('=')[0].strip()}")
    
    with open(alert_model_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info("Alert模型清理完成")

def cleanup_performance_model():
    """清理PerformanceMetric模型中未使用的字段"""
    performance_model_path = project_root / "app" / "models" / "performance.py"
    
    logger.info("清理PerformanceMetric模型中的未使用字段...")
    
    with open(performance_model_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 移除基线相关字段
    lines_to_remove = [
        "baseline_value = Column(String(100), nullable=True, comment=\"基线值\")",
    ]
    
    for line in lines_to_remove:
        if line in content:
            content = content.replace(line + "\n", "")
            logger.info(f"移除字段: {line.split('=')[0].strip()}")
    
    with open(performance_model_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info("PerformanceMetric模型清理完成")

def cleanup_unused_imports():
    """清理未使用的导入"""
    files_to_check = [
        "app/api/v1/endpoints/ai.py",
    ]
    
    for file_path in files_to_check:
        full_path = project_root / file_path
        if not full_path.exists():
            continue
            
        logger.info(f"检查文件: {file_path}")
        
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除未使用的导入
        unused_imports = [
            "from typing import List, Optional",
            "from fastapi import APIRouter, Depends, HTTPException, Body",
            "from ....schemas.ai import ConversationCreate, MessageCreate, AnalysisRequest, ChatRequest",
        ]
        
        # 替换为实际使用的导入
        if "from typing import List, Optional" in content:
            content = content.replace(
                "from typing import List, Optional",
                "from typing import Optional"
            )
            logger.info("更新typing导入，移除未使用的List")
        
        if "from fastapi import APIRouter, Depends, HTTPException, Body" in content:
            content = content.replace(
                "from fastapi import APIRouter, Depends, HTTPException, Body",
                "from fastapi import APIRouter, Depends, HTTPException"
            )
            logger.info("更新fastapi导入，移除未使用的Body")
        
        if "from ....schemas.ai import ConversationCreate, MessageCreate, AnalysisRequest, ChatRequest" in content:
            content = content.replace(
                "from ....schemas.ai import ConversationCreate, MessageCreate, AnalysisRequest, ChatRequest",
                "from ....schemas.ai import AnalysisRequest, ChatRequest"
            )
            logger.info("更新schemas导入，移除未使用的ConversationCreate, MessageCreate")
        
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)

def generate_migration_script():
    """生成数据库迁移脚本"""
    migration_script = """
-- 清理未使用字段的数据库迁移脚本
-- 执行前请备份数据库

-- 移除Device表中的未使用字段
ALTER TABLE devices DROP COLUMN IF EXISTS last_backup_time;
ALTER TABLE devices DROP COLUMN IF EXISTS backup_status;

-- 移除Alert表中的未使用字段  
ALTER TABLE alerts DROP COLUMN IF EXISTS escalation_level;

-- 移除PerformanceMetric表中的未使用字段
ALTER TABLE performance_metrics DROP COLUMN IF EXISTS baseline_value;

-- 验证字段已被移除
DESCRIBE devices;
DESCRIBE alerts;
DESCRIBE performance_metrics;
"""
    
    migration_path = project_root / "cleanup_migration.sql"
    with open(migration_path, 'w', encoding='utf-8') as f:
        f.write(migration_script)
    
    logger.info(f"数据库迁移脚本已生成: {migration_path}")

def main():
    """主函数"""
    logger.info("开始清理未使用的代码和字段...")
    
    try:
        # 清理数据库模型
        cleanup_device_model()
        cleanup_alert_model()
        cleanup_performance_model()
        
        # 清理未使用的导入
        cleanup_unused_imports()
        
        # 生成数据库迁移脚本
        generate_migration_script()
        
        logger.info("✅ 代码清理完成！")
        logger.info("📋 清理总结:")
        logger.info("  - 移除了Device模型中的备份相关字段")
        logger.info("  - 移除了Alert模型中的升级相关字段")
        logger.info("  - 移除了PerformanceMetric模型中的基线相关字段")
        logger.info("  - 清理了AI端点中的未使用导入")
        logger.info("  - 生成了数据库迁移脚本")
        logger.info("")
        logger.info("⚠️  注意事项:")
        logger.info("  1. 请在执行数据库迁移前备份数据库")
        logger.info("  2. 执行 cleanup_migration.sql 来更新数据库结构")
        logger.info("  3. 重新启动应用程序以应用更改")
        
    except Exception as e:
        logger.error(f"清理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    main()

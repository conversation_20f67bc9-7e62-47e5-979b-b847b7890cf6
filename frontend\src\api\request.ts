import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'ant-design-vue'

// Create axios instance
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL + import.meta.env.VITE_API_PREFIX,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // Add auth token if available
    const token = localStorage.getItem('token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response
    
    // Handle successful response
    if (response.status >= 200 && response.status < 300) {
      return data
    }
    
    // Handle error response
    const errorMessage = data?.detail || data?.message || '请求失败'
    message.error(errorMessage)
    return Promise.reject(new Error(errorMessage))
  },
  (error) => {
    console.error('Response error:', error)
    
    // Handle different error status codes
    if (error.response) {
      const { status, data } = error.response
      let errorMessage = data?.detail || data?.message || '请求失败'
      
      switch (status) {
        case 400:
          errorMessage = '请求参数错误'
          break
        case 401:
          errorMessage = '未授权，请重新登录'
          // Clear token and redirect to login
          localStorage.removeItem('token')
          window.location.href = '/login'
          break
        case 403:
          errorMessage = '拒绝访问'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        case 502:
          errorMessage = '网关错误'
          break
        case 503:
          errorMessage = '服务不可用'
          break
        default:
          errorMessage = `请求失败 (${status})`
      }
      
      message.error(errorMessage)
      return Promise.reject(new Error(errorMessage))
    } else if (error.request) {
      // Network error
      const errorMessage = '网络连接失败，请检查网络设置'
      message.error(errorMessage)
      return Promise.reject(new Error(errorMessage))
    } else {
      // Other error
      const errorMessage = error.message || '未知错误'
      message.error(errorMessage)
      return Promise.reject(new Error(errorMessage))
    }
  }
)

export default request

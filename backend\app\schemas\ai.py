"""
AI conversation and analysis Pydantic schemas
"""
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime

from ..models.ai_conversation import ConversationStatus, MessageRole, AnalysisType


class ConversationBase(BaseModel):
    """Base conversation schema"""
    title: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    user_id: Optional[str] = Field(None, max_length=100)
    user_name: Optional[str] = Field(None, max_length=100)
    session_id: Optional[str] = Field(None, max_length=100)


class ConversationCreate(ConversationBase):
    """Schema for creating conversation"""
    pass


class ConversationResponse(ConversationBase):
    """Schema for conversation response"""
    id: int
    status: ConversationStatus
    total_messages: int
    total_tokens: Optional[int] = None
    analysis_type: Optional[AnalysisType] = None
    started_at: Optional[str] = None
    last_activity: Optional[str] = None
    completed_at: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    created_at: datetime
    updated_at: datetime
    is_active: bool
    
    class Config:
        from_attributes = True


class MessageBase(BaseModel):
    """Base message schema"""
    role: MessageRole
    content: str = Field(..., min_length=1)


class MessageCreate(MessageBase):
    """Schema for creating message"""
    conversation_id: int
    token_count: Optional[int] = None
    model_used: Optional[str] = None
    tool_calls: Optional[Dict[str, Any]] = None
    tool_results: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class MessageResponse(MessageBase):
    """Schema for message response"""
    id: int
    conversation_id: int
    token_count: Optional[int] = None
    model_used: Optional[str] = None
    timestamp: str
    processing_time: Optional[float] = None
    tool_calls: Optional[Dict[str, Any]] = None
    tool_results: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime
    is_active: bool
    
    class Config:
        from_attributes = True


class AnalysisRequest(BaseModel):
    """Schema for analysis request"""
    analysis_type: AnalysisType
    query: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    device_id: Optional[int] = None
    conversation_id: Optional[int] = None


class AnalysisResponse(BaseModel):
    """Schema for analysis response"""
    id: int
    analysis_type: AnalysisType
    title: str
    summary: Optional[str] = None
    input_data: Optional[Dict[str, Any]] = None
    query: Optional[str] = None
    findings: Optional[Dict[str, Any]] = None
    recommendations: Optional[Dict[str, Any]] = None
    confidence_score: Optional[float] = None
    model_used: Optional[str] = None
    processing_time: Optional[float] = None
    token_usage: Optional[Dict[str, Any]] = None
    conversation_id: Optional[int] = None
    device_id: Optional[int] = None
    analyzed_at: str
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime
    is_active: bool
    
    class Config:
        from_attributes = True


class ChatRequest(BaseModel):
    """Schema for chat request"""
    message: str = Field(..., min_length=1, max_length=4000)
    conversation_id: Optional[int] = None
    user_id: Optional[str] = Field(None, max_length=100)
    session_id: Optional[str] = Field(None, max_length=100)
    context: Optional[Dict[str, Any]] = None


class ChatResponse(BaseModel):
    """Schema for chat response"""
    conversation_id: int
    message: str
    timestamp: str
    model_used: Optional[str] = None
    token_count: Optional[int] = None
    processing_time: Optional[float] = None


class AIStats(BaseModel):
    """AI service statistics schema"""
    total_conversations: int
    active_conversations: int
    total_messages: int
    total_analyses: int
    average_response_time: float
    total_tokens_used: int
    most_common_analysis_type: Optional[str] = None


class AgentInfo(BaseModel):
    """Agent information schema"""
    key: str
    name: str
    description: str


class AgentInfoResponse(BaseModel):
    """Agent info response schema"""
    agents: List[AgentInfo]
    total_agents: int
    timestamp: str


class AgentTestResult(BaseModel):
    """Agent test result schema"""
    status: str
    response: Optional[str] = None
    agent_name: Optional[str] = None
    timestamp: str
    error: Optional[str] = None


class AgentTestResponse(BaseModel):
    """Agent test response schema"""
    connection_test: Dict[str, AgentTestResult]
    timestamp: str
    total_agents: int
    successful_connections: int

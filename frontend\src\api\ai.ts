import request from './request'

export interface ChatRequest {
  message: string
  conversation_id?: string
  user_id?: string
  session_id?: string
  context?: Record<string, any>
}

export interface ChatResponse {
  conversation_id: number
  message: string
  timestamp: string
  model_used?: string
  token_count?: number
  processing_time?: number
}

export interface AnalysisRequest {
  analysis_type: string
  query?: string
  data?: Record<string, any>
  device_id?: number
  conversation_id?: number
}

export interface AnalysisResponse {
  id: number
  analysis_type: string
  title: string
  summary?: string
  input_data?: Record<string, any>
  query?: string
  findings?: Record<string, any>
  recommendations?: Record<string, any>
  confidence_score?: number
  model_used?: string
  processing_time?: number
  token_usage?: Record<string, any>
  conversation_id?: number
  device_id?: number
  analyzed_at: string
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
  is_active: boolean
}

export interface AgentInfo {
  name: string
  instructions: string
  tools_count: number
}

export interface AgentChatRequest {
  message: string
  conversation_id?: string
}

export interface AgentChatResponse {
  conversation_id: number
  agent_type: string
  agent_name: string
  response: string
  timestamp: string
}

export const aiApi = {
  // General chat
  chat(data: ChatRequest) {
    return request.post<ChatResponse>('/ai/chat', data)
  },

  // Analysis
  analyze(data: AnalysisRequest) {
    return request.post<AnalysisResponse>('/ai/analyze', data)
  },

  // Get conversations
  getConversations(params?: {
    user_id?: string
    status?: string
    skip?: number
    limit?: number
  }) {
    return request.get('/ai/conversations', { params })
  },

  // Get conversation messages
  getConversationMessages(conversationId: number) {
    return request.get(`/ai/conversations/${conversationId}/messages`)
  },

  // Get analysis results
  getAnalysisResults(params?: {
    analysis_type?: string
    device_id?: number
    skip?: number
    limit?: number
  }) {
    return request.get<AnalysisResponse[]>('/ai/analysis-results', { params })
  },

  // Get available agents
  getAgents() {
    return request.get<{
      agents: Record<string, AgentInfo>
      total_agents: number
      timestamp: string
    }>('/ai/agents')
  },

  // Test AI connection
  testConnection() {
    return request.post<{
      success: boolean
      message: string
      response?: string
      timestamp: string
    }>('/ai/test-connection')
  },

  // Chat with specific agent
  chatWithAgent(agentType: string, message: string, conversationId?: string) {
    return request.post<AgentChatResponse>(`/ai/chat/${agentType}`, {
      message,
      conversation_id: conversationId
    })
  }
}

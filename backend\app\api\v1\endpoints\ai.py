"""
AI analysis and conversation endpoints
"""
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from loguru import logger

from ....core.database import get_db
from ....models.ai_conversation import Conversation, Message, AnalysisResult, ConversationStatus, MessageRole, AnalysisType
from ....schemas.ai import ConversationCreate, MessageCreate, AnalysisRequest, ChatRequest

router = APIRouter()


@router.post("/chat")
async def chat_with_ai(
    request: ChatRequest,
    db: Session = Depends(get_db)
):
    """Chat with AI assistant"""
    try:
        # Get or create conversation
        conversation = None
        if request.conversation_id:
            conversation = db.query(Conversation).filter(
                Conversation.id == request.conversation_id,
                Conversation.is_active == True
            ).first()
        
        if not conversation:
            # Create new conversation
            conversation = Conversation(
                title=request.message[:50] + "..." if len(request.message) > 50 else request.message,
                status=ConversationStatus.ACTIVE,
                user_id=request.user_id,
                session_id=request.session_id,
                started_at=datetime.now().isoformat(),
                last_activity=datetime.now().isoformat(),
                total_messages=0
            )
            db.add(conversation)
            db.commit()
            db.refresh(conversation)
        
        # Add user message
        user_message = Message(
            conversation_id=conversation.id,
            role=MessageRole.USER,
            content=request.message,
            timestamp=datetime.now().isoformat()
        )
        db.add(user_message)
        
        # 直接使用新的Agent系统
        from ....agents import chat_with_agent

        # 确定Agent类型，默认使用通用助手
        agent_type = getattr(request, 'agent_type', 'general')

        # 与Agent对话
        agent_result = await chat_with_agent(
            agent_type=agent_type,
            message=request.message,
            context={'conversation_id': str(conversation.id)}
        )

        if agent_result["success"]:
            ai_response = agent_result["response"]
        else:
            ai_response = f"抱歉，AI服务暂时不可用：{agent_result.get('error', '未知错误')}"
        
        # Add AI response message
        ai_message = Message(
            conversation_id=conversation.id,
            role=MessageRole.ASSISTANT,
            content=ai_response,
            timestamp=datetime.now().isoformat(),
            model_used="deepseek-chat"
        )
        db.add(ai_message)
        
        # Update conversation
        conversation.total_messages += 2
        conversation.last_activity = datetime.now().isoformat()
        
        db.commit()
        
        return {
            "conversation_id": conversation.id,
            "message": ai_response,
            "timestamp": ai_message.timestamp
        }
        
    except Exception as e:
        logger.error(f"Error in AI chat: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to process chat request")


@router.post("/analyze")
async def analyze_data(
    request: AnalysisRequest,
    db: Session = Depends(get_db)
):
    """Perform AI analysis on network data"""
    try:
        # 使用新的Agent系统进行分析
        from ....agents import chat_with_agent

        # 根据分析类型选择合适的Agent
        if request.analysis_type.value in ['security_analysis', 'threat_detection']:
            agent_type = 'security'
        elif request.analysis_type.value in ['performance_analysis', 'capacity_planning']:
            agent_type = 'performance'
        else:
            agent_type = 'general'

        # 构建分析查询
        analysis_query = f"请分析以下数据：{request.query}\n数据内容：{request.data}"

        # 执行分析
        agent_result = await chat_with_agent(
            agent_type=agent_type,
            message=analysis_query,
            context={'analysis_type': request.analysis_type.value}
        )

        analysis_result_data = {
            "success": agent_result.get("success", False),
            "analysis_result": agent_result.get("response", "分析失败"),
            "agent_used": agent_result.get("agent_name", "Unknown"),
            "analysis_type": request.analysis_type.value,
            "timestamp": agent_result.get("timestamp", datetime.now().isoformat())
        }

        if analysis_result_data["success"]:
            analysis_result = AnalysisResult(
                analysis_type=request.analysis_type,
                title=f"{request.analysis_type.value.replace('_', ' ').title()} Analysis",
                summary=analysis_result_data["analysis_result"][:200] + "..." if len(analysis_result_data["analysis_result"]) > 200 else analysis_result_data["analysis_result"],
                input_data=request.data,
                query=request.query,
                findings={
                    "analysis_content": analysis_result_data["analysis_result"],
                    "agent_used": analysis_result_data["agent_used"]
                },
                recommendations={
                    "analysis_type": analysis_result_data["analysis_type"],
                    "timestamp": analysis_result_data["timestamp"]
                },
                confidence_score=0.85,
                model_used="deepseek-chat",
                processing_time=1.5,
                analyzed_at=datetime.now().isoformat(),
                conversation_id=request.conversation_id,
                device_id=request.device_id
            )
        else:
            analysis_result = AnalysisResult(
                analysis_type=request.analysis_type,
                title=f"{request.analysis_type.value.replace('_', ' ').title()} Analysis Failed",
                summary=f"分析失败：{analysis_result_data.get('error', '未知错误')}",
                input_data=request.data,
                query=request.query,
                findings={"error": analysis_result_data.get('error', '未知错误')},
                recommendations={},
                confidence_score=0.0,
                model_used="deepseek-chat",
                processing_time=0.5,
                analyzed_at=datetime.now().isoformat(),
                conversation_id=request.conversation_id,
                device_id=request.device_id
            )
        
        db.add(analysis_result)
        db.commit()
        db.refresh(analysis_result)
        
        return analysis_result
        
    except Exception as e:
        logger.error(f"Error in AI analysis: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to perform analysis")


@router.get("/conversations")
async def get_conversations(
    user_id: Optional[str] = None,
    status: Optional[ConversationStatus] = None,
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Get conversation history"""
    try:
        query = db.query(Conversation).filter(Conversation.is_active == True)
        
        if user_id:
            query = query.filter(Conversation.user_id == user_id)
        if status:
            query = query.filter(Conversation.status == status)
            
        conversations = query.order_by(Conversation.last_activity.desc()).offset(skip).limit(limit).all()
        return conversations
        
    except Exception as e:
        logger.error(f"Error getting conversations: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve conversations")


@router.get("/conversations/{conversation_id}/messages")
async def get_conversation_messages(
    conversation_id: int,
    db: Session = Depends(get_db)
):
    """Get messages from a conversation"""
    try:
        conversation = db.query(Conversation).filter(
            Conversation.id == conversation_id,
            Conversation.is_active == True
        ).first()
        
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        messages = db.query(Message).filter(
            Message.conversation_id == conversation_id,
            Message.is_active == True
        ).order_by(Message.timestamp.asc()).all()
        
        return {
            "conversation": conversation,
            "messages": messages
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation messages: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve conversation messages")


@router.get("/analysis-results")
async def get_analysis_results(
    analysis_type: Optional[AnalysisType] = None,
    device_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Get analysis results"""
    try:
        query = db.query(AnalysisResult).filter(AnalysisResult.is_active == True)
        
        if analysis_type:
            query = query.filter(AnalysisResult.analysis_type == analysis_type)
        if device_id:
            query = query.filter(AnalysisResult.device_id == device_id)
            
        results = query.order_by(AnalysisResult.analyzed_at.desc()).offset(skip).limit(limit).all()
        return results
        
    except Exception as e:
        logger.error(f"Error getting analysis results: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve analysis results")


@router.get("/agents")
async def get_available_agents():
    """Get available AI agents"""
    try:
        from ....agents import get_available_agents as get_agents_list

        agents_list = get_agents_list()
        # 转换为前端期望的格式
        agents_dict = {}
        for agent in agents_list:
            agents_dict[agent['key']] = {
                "name": agent['name'],
                "instructions": agent['description'],
                "tools_count": 4 if agent['key'] == 'general' else 5 if agent['key'] == 'security' else 4
            }

        return {
            "agents": agents_dict,
            "total_agents": len(agents_list),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting available agents: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve available agents")


@router.post("/test-connection")
async def test_ai_connection():
    """Test AI service connection"""
    try:
        from ....agents import test_agent_connections

        result = await test_agent_connections()
        # 检查是否所有Agent连接成功
        successful_count = len([r for r in result.values() if r.get('status') == 'success'])
        all_success = successful_count == len(result)

        return {
            "success": all_success,
            "message": f"Agent连接测试完成，{successful_count}/{len(result)}个Agent连接成功",
            "response": f"详细结果：{result}",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error testing AI connection: {e}")
        raise HTTPException(status_code=500, detail="Failed to test AI connection")


@router.post("/chat/{agent_type}")
async def chat_with_specific_agent(
    agent_type: str,
    message: str,
    conversation_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Chat with specific agent type"""
    try:
        from ....agents import chat_with_agent

        # Get or create conversation
        conversation = None
        if conversation_id:
            conversation = db.query(Conversation).filter(
                Conversation.id == int(conversation_id),
                Conversation.is_active == True
            ).first()

        if not conversation:
            conversation = Conversation(
                title=f"{agent_type.title()} Chat: {message[:50]}...",
                status=ConversationStatus.ACTIVE,
                started_at=datetime.now().isoformat(),
                last_activity=datetime.now().isoformat(),
                total_messages=0
            )
            db.add(conversation)
            db.commit()
            db.refresh(conversation)

        # Chat with specific agent
        result = await chat_with_agent(
            agent_type=agent_type,
            message=message,
            context={'conversation_id': str(conversation.id)}
        )

        if result.get("success", False):
            # Add messages to database
            user_message = Message(
                conversation_id=conversation.id,
                role=MessageRole.USER,
                content=message,
                timestamp=datetime.now().isoformat()
            )
            db.add(user_message)

            ai_message = Message(
                conversation_id=conversation.id,
                role=MessageRole.ASSISTANT,
                content=result["response"],
                timestamp=datetime.now().isoformat(),
                model_used="deepseek-chat"
            )
            db.add(ai_message)

            conversation.total_messages += 2
            conversation.last_activity = datetime.now().isoformat()
            db.commit()

            return {
                "conversation_id": conversation.id,
                "agent_type": agent_type,
                "agent_name": result.get("agent_name", "Unknown"),
                "response": result["response"],
                "timestamp": result.get("timestamp", datetime.now().isoformat())
            }
        else:
            raise HTTPException(status_code=500, detail=result.get("error", "Agent communication failed"))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in agent chat: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to chat with agent")

"""
Performance monitoring and metrics models
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import Column, String, Integer, Float, Text, JSON, ForeignKey, Index
from sqlalchemy.orm import relationship

from .base import BaseModel


class PerformanceMetric(BaseModel):
    """Performance metrics model"""
    __tablename__ = "performance_metrics"
    
    # Device Association
    device_id = Column(Integer, ForeignKey("devices.id"), nullable=False)
    device_ip = Column(String(45), nullable=False, index=True)
    
    # Metric Information
    metric_name = Column(String(100), nullable=False, index=True)  # cpu_usage, memory_usage, etc.
    metric_value = Column(Float, nullable=False)
    metric_unit = Column(String(20), nullable=True)  # %, MB, GB, etc.
    
    # Timing
    timestamp = Column(String(50), nullable=False, index=True)  # ISO string
    collection_interval = Column(Integer, nullable=True)  # Seconds
    
    # Additional Data
    metadata = Column(JSON, nullable=True)  # Additional metric data
    tags = Column(JSON, nullable=True)  # Metric tags
    
    # Relationships
    device = relationship("Device", back_populates="performance_metrics")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_device_metric_time', 'device_id', 'metric_name', 'timestamp'),
        Index('idx_device_time', 'device_id', 'timestamp'),
        Index('idx_metric_time', 'metric_name', 'timestamp'),
    )
    
    def __repr__(self):
        return f"<PerformanceMetric(device_id={self.device_id}, metric='{self.metric_name}', value={self.metric_value})>"


class NetworkTopology(BaseModel):
    """Network topology model"""
    __tablename__ = "network_topology"
    
    # Node Information
    node_id = Column(String(100), nullable=False, unique=True, index=True)
    node_type = Column(String(50), nullable=False)  # device, subnet, etc.
    node_name = Column(String(255), nullable=False)
    node_ip = Column(String(45), nullable=True)
    
    # Position Information (for visualization)
    x_position = Column(Float, nullable=True)
    y_position = Column(Float, nullable=True)
    z_position = Column(Float, nullable=True)
    
    # Node Properties
    properties = Column(JSON, nullable=True)  # Node-specific properties
    style = Column(JSON, nullable=True)  # Visualization style
    
    def __repr__(self):
        return f"<NetworkTopology(node_id='{self.node_id}', type='{self.node_type}', name='{self.node_name}')>"


class NetworkConnection(BaseModel):
    """Network connections/edges model"""
    __tablename__ = "network_connections"
    
    # Connection Information
    source_node_id = Column(String(100), nullable=False, index=True)
    target_node_id = Column(String(100), nullable=False, index=True)
    connection_type = Column(String(50), nullable=False)  # ethernet, wifi, etc.
    
    # Connection Properties
    bandwidth = Column(Float, nullable=True)  # Mbps
    latency = Column(Float, nullable=True)  # ms
    packet_loss = Column(Float, nullable=True)  # %
    utilization = Column(Float, nullable=True)  # %
    
    # Connection Status
    status = Column(String(20), nullable=False, default="active")  # active, inactive, down
    
    # Additional Data
    properties = Column(JSON, nullable=True)  # Connection-specific properties
    style = Column(JSON, nullable=True)  # Visualization style
    
    # Indexes
    __table_args__ = (
        Index('idx_source_target', 'source_node_id', 'target_node_id'),
    )
    
    def __repr__(self):
        return f"<NetworkConnection(source='{self.source_node_id}', target='{self.target_node_id}', type='{self.connection_type}')>"

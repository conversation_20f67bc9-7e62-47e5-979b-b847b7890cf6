<template>
  <div class="device-discovery">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>设备发现</h1>
      <a-button @click="$router.back()">
        <template #icon><ArrowLeftOutlined /></template>
        返回
      </a-button>
    </div>

    <!-- 发现配置 -->
    <a-card title="发现配置" class="config-card">
      <a-form
        :model="discoveryForm"
        :rules="discoveryRules"
        ref="discoveryFormRef"
        layout="vertical"
        @finish="startDiscovery"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="IP范围" name="ip_range">
              <a-input
                v-model:value="discoveryForm.ip_range"
                placeholder="例如: ***********/24 或 ***********-*************"
              />
              <template #extra>
                支持CIDR格式 (***********/24) 或范围格式 (***********-*************)
              </template>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="扫描类型" name="scan_type">
              <a-select v-model:value="discoveryForm.scan_type">
                <a-select-option value="ping">Ping扫描 (快速)</a-select-option>
                <a-select-option value="snmp">SNMP扫描 (详细)</a-select-option>
                <a-select-option value="full">完整扫描 (最详细)</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="超时时间 (秒)" name="timeout">
              <a-input-number
                v-model:value="discoveryForm.timeout"
                :min="1"
                :max="60"
                style="width: 100%;"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="并发限制" name="concurrent_limit">
              <a-input-number
                v-model:value="discoveryForm.concurrent_limit"
                :min="1"
                :max="100"
                style="width: 100%;"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item>
          <a-space>
            <a-button
              type="primary"
              html-type="submit"
              :loading="discoveryLoading"
              :disabled="!!currentTask"
            >
              <template #icon><SearchOutlined /></template>
              开始发现
            </a-button>
            <a-button
              v-if="currentTask"
              danger
              @click="cancelDiscovery"
              :loading="cancelLoading"
            >
              <template #icon><StopOutlined /></template>
              取消发现
            </a-button>
            <a-button @click="resetForm">
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 发现进度 -->
    <a-card v-if="currentTask" title="发现进度" class="progress-card">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="任务ID">
          {{ currentTask.task_id }}
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(currentTask.status)">
            {{ getStatusLabel(currentTask.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="开始时间">
          {{ formatTime(currentTask.scan_start_time) }}
        </a-descriptions-item>
        <a-descriptions-item label="已扫描">
          {{ currentTask.total_scanned }} 个IP
        </a-descriptions-item>
        <a-descriptions-item label="发现设备">
          {{ currentTask.discovered_devices?.length || 0 }} 台
        </a-descriptions-item>
        <a-descriptions-item label="进度">
          <a-progress
            :percent="currentTask.progress"
            :status="currentTask.status === 'failed' ? 'exception' : undefined"
          />
        </a-descriptions-item>
      </a-descriptions>

      <!-- 错误信息 -->
      <div v-if="currentTask.errors?.length" class="errors-section">
        <a-divider>错误信息</a-divider>
        <a-alert
          v-for="error in currentTask.errors"
          :key="error"
          :message="error"
          type="error"
          show-icon
          style="margin-bottom: 8px;"
        />
      </div>
    </a-card>

    <!-- 发现结果 -->
    <a-card v-if="discoveredDevices.length > 0" title="发现的设备" class="results-card">
      <template #extra>
        <a-space>
          <a-button @click="exportResults">
            <template #icon><DownloadOutlined /></template>
            导出结果
          </a-button>
          <a-button type="primary" @click="addSelectedDevices" :disabled="selectedDevices.length === 0">
            <template #icon><PlusOutlined /></template>
            添加选中设备 ({{ selectedDevices.length }})
          </a-button>
        </a-space>
      </template>

      <!-- 统计信息 -->
      <a-row :gutter="16" class="stats-row">
        <a-col :span="6">
          <a-statistic title="总发现数" :value="discoveredDevices.length" />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="高置信度"
            :value="discoveredDevices.filter(d => d.confidence_score > 0.8).length"
            :value-style="{ color: '#52c41a' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="SNMP设备"
            :value="discoveredDevices.filter(d => d.snmp_community).length"
            :value-style="{ color: '#1890ff' }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic
            title="平均响应时间"
            :value="getAverageResponseTime()"
            suffix="ms"
            :value-style="{ color: '#722ed1' }"
          />
        </a-col>
      </a-row>

      <!-- 设备表格 -->
      <a-table
        :columns="deviceColumns"
        :data-source="discoveredDevices"
        :row-selection="{
          selectedRowKeys: selectedDevices,
          onChange: onSelectionChange,
          getCheckboxProps: (record) => ({ disabled: record.confidence_score < 0.5 })
        }"
        :pagination="{ pageSize: 10 }"
        size="small"
        class="devices-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'ip_address'">
            <a-typography-text copyable>{{ record.ip_address }}</a-typography-text>
          </template>
          <template v-else-if="column.key === 'device_type'">
            <a-tag v-if="record.device_type" color="blue">
              {{ getDeviceTypeIcon(record.device_type) }} {{ record.device_type }}
            </a-tag>
            <span v-else>-</span>
          </template>
          <template v-else-if="column.key === 'confidence_score'">
            <a-progress
              :percent="Math.round(record.confidence_score * 100)"
              size="small"
              :stroke-color="getConfidenceColor(record.confidence_score)"
            />
          </template>
          <template v-else-if="column.key === 'response_time'">
            {{ record.response_time }}ms
          </template>
          <template v-else-if="column.key === 'open_ports'">
            <a-tag v-for="port in record.open_ports.slice(0, 3)" :key="port" size="small">
              {{ port }}
            </a-tag>
            <a-tag v-if="record.open_ports.length > 3" size="small">
              +{{ record.open_ports.length - 3 }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'snmp_community'">
            <a-tag v-if="record.snmp_community" color="green">
              {{ record.snmp_community }}
            </a-tag>
            <span v-else>-</span>
          </template>
          <template v-else-if="column.key === 'last_seen'">
            {{ formatTime(record.last_seen) }}
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button size="small" @click="viewDeviceDetails(record)">
                详情
              </a-button>
              <a-button size="small" type="primary" @click="addSingleDevice(record)">
                添加
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 设备详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="设备详情"
      :footer="null"
      width="700px"
    >
      <a-descriptions v-if="selectedDevice" :column="2" bordered>
        <a-descriptions-item label="IP地址" :span="2">
          <a-typography-text copyable>{{ selectedDevice.ip_address }}</a-typography-text>
        </a-descriptions-item>
        <a-descriptions-item label="主机名">
          {{ selectedDevice.hostname || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="MAC地址">
          {{ selectedDevice.mac_address || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="设备类型">
          {{ selectedDevice.device_type || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="厂商">
          {{ selectedDevice.vendor || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="型号">
          {{ selectedDevice.model || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="操作系统">
          {{ selectedDevice.os_info || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="响应时间">
          {{ selectedDevice.response_time }}ms
        </a-descriptions-item>
        <a-descriptions-item label="置信度">
          <a-progress
            :percent="Math.round(selectedDevice.confidence_score * 100)"
            :stroke-color="getConfidenceColor(selectedDevice.confidence_score)"
          />
        </a-descriptions-item>
        <a-descriptions-item label="SNMP团体名">
          {{ selectedDevice.snmp_community || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="最后发现">
          {{ formatTime(selectedDevice.last_seen) }}
        </a-descriptions-item>
        <a-descriptions-item label="开放端口" :span="2">
          <a-tag v-for="port in selectedDevice.open_ports" :key="port" style="margin: 2px;">
            {{ port }}
          </a-tag>
          <span v-if="selectedDevice.open_ports.length === 0">无</span>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  SearchOutlined,
  StopOutlined,
  DownloadOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'
import { monitoringApi, monitoringUtils, type DeviceDiscoveryRequest, type DeviceDiscoveryResponse, type DiscoveredDevice } from '@/api/monitoring'

// 响应式数据
const discoveryLoading = ref(false)
const cancelLoading = ref(false)
const detailModalVisible = ref(false)

const discoveryFormRef = ref()
const discoveryForm = reactive<DeviceDiscoveryRequest>({
  ip_range: '***********/24',
  scan_type: 'ping',
  timeout: 5,
  concurrent_limit: 20
})

const currentTask = ref<DeviceDiscoveryResponse | null>(null)
const discoveredDevices = ref<DiscoveredDevice[]>([])
const selectedDevices = ref<string[]>([])
const selectedDevice = ref<DiscoveredDevice | null>(null)

let pollTimer: NodeJS.Timeout | null = null

// 表单验证规则
const discoveryRules = {
  ip_range: [
    { required: true, message: '请输入IP范围' },
    { validator: (rule: any, value: string) => {
      if (value && !monitoringUtils.validateIPRange(value)) {
        return Promise.reject('请输入有效的IP范围')
      }
      return Promise.resolve()
    }}
  ],
  scan_type: [{ required: true, message: '请选择扫描类型' }],
  timeout: [{ required: true, message: '请输入超时时间' }],
  concurrent_limit: [{ required: true, message: '请输入并发限制' }]
}

// 表格列定义
const deviceColumns = [
  { title: 'IP地址', dataIndex: 'ip_address', key: 'ip_address', width: 140 },
  { title: '主机名', dataIndex: 'hostname', key: 'hostname', width: 150, ellipsis: true },
  { title: '设备类型', dataIndex: 'device_type', key: 'device_type', width: 120 },
  { title: '厂商', dataIndex: 'vendor', key: 'vendor', width: 100, ellipsis: true },
  { title: '置信度', dataIndex: 'confidence_score', key: 'confidence_score', width: 100 },
  { title: '响应时间', dataIndex: 'response_time', key: 'response_time', width: 100 },
  { title: '开放端口', dataIndex: 'open_ports', key: 'open_ports', width: 150 },
  { title: 'SNMP', dataIndex: 'snmp_community', key: 'snmp_community', width: 80 },
  { title: '发现时间', dataIndex: 'last_seen', key: 'last_seen', width: 150 },
  { title: '操作', key: 'actions', width: 120, fixed: 'right' }
]

// 工具函数
const formatTime = (timestamp?: string) => {
  return monitoringUtils.formatTimestamp(timestamp)
}

const getStatusColor = (status: string) => {
  return monitoringUtils.getStatusColor(status)
}

const getStatusLabel = (status: string) => {
  const labels = {
    started: '已启动',
    running: '运行中',
    completed: '已完成',
    failed: '失败'
  }
  return labels[status as keyof typeof labels] || status
}

const getConfidenceColor = (score: number) => {
  return monitoringUtils.getConfidenceColor(score)
}

const getDeviceTypeIcon = (deviceType?: string) => {
  return monitoringUtils.getDeviceTypeIcon(deviceType)
}

const getAverageResponseTime = () => {
  if (discoveredDevices.value.length === 0) return 0
  const total = discoveredDevices.value.reduce((sum, device) => sum + device.response_time, 0)
  return Math.round(total / discoveredDevices.value.length)
}

// 事件处理
const startDiscovery = async () => {
  try {
    await discoveryFormRef.value.validate()
    discoveryLoading.value = true
    
    const response = await monitoringApi.discoverDevices(discoveryForm)
    currentTask.value = response.data
    discoveredDevices.value = []
    selectedDevices.value = []
    
    message.success('设备发现已启动')
    startPolling()
  } catch (error) {
    console.error('启动设备发现失败:', error)
    message.error('启动设备发现失败')
  } finally {
    discoveryLoading.value = false
  }
}

const cancelDiscovery = async () => {
  if (!currentTask.value) return
  
  try {
    cancelLoading.value = true
    await monitoringApi.cancelDiscovery(currentTask.value.task_id)
    message.success('设备发现已取消')
    stopPolling()
    currentTask.value = null
  } catch (error) {
    console.error('取消设备发现失败:', error)
    message.error('取消设备发现失败')
  } finally {
    cancelLoading.value = false
  }
}

const resetForm = () => {
  discoveryForm.ip_range = '***********/24'
  discoveryForm.scan_type = 'ping'
  discoveryForm.timeout = 5
  discoveryForm.concurrent_limit = 20
  discoveryFormRef.value?.resetFields()
}

const onSelectionChange = (selectedRowKeys: string[]) => {
  selectedDevices.value = selectedRowKeys
}

const viewDeviceDetails = (device: DiscoveredDevice) => {
  selectedDevice.value = device
  detailModalVisible.value = true
}

const addSingleDevice = async (device: DiscoveredDevice) => {
  try {
    // 这里应该调用添加设备的API
    message.success(`设备 ${device.ip_address} 添加成功`)
  } catch (error) {
    console.error('添加设备失败:', error)
    message.error('添加设备失败')
  }
}

const addSelectedDevices = async () => {
  try {
    const devicesToAdd = discoveredDevices.value.filter(device => 
      selectedDevices.value.includes(device.ip_address)
    )
    
    // 这里应该批量调用添加设备的API
    message.success(`已添加 ${devicesToAdd.length} 台设备`)
    selectedDevices.value = []
  } catch (error) {
    console.error('批量添加设备失败:', error)
    message.error('批量添加设备失败')
  }
}

const exportResults = () => {
  const data = JSON.stringify(discoveredDevices.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `device_discovery_${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  message.success('导出成功')
}

// 轮询函数
const startPolling = () => {
  if (pollTimer) return
  
  pollTimer = setInterval(async () => {
    if (!currentTask.value) {
      stopPolling()
      return
    }
    
    try {
      const response = await monitoringApi.getDiscoveryStatus(currentTask.value.task_id)
      currentTask.value = response.data
      
      if (response.data.discovered_devices) {
        discoveredDevices.value = response.data.discovered_devices
      }
      
      if (response.data.status === 'completed' || response.data.status === 'failed') {
        stopPolling()
        if (response.data.status === 'completed') {
          message.success('设备发现完成')
        } else {
          message.error('设备发现失败')
        }
      }
    } catch (error) {
      console.error('获取发现状态失败:', error)
      stopPolling()
    }
  }, 2000)
}

const stopPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null
  }
}

// 组件挂载和卸载
onMounted(() => {
  // 组件挂载时的初始化
})

onUnmounted(() => {
  stopPolling()
})
</script>

<style scoped>
.device-discovery {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.config-card {
  margin-bottom: 24px;
}

.progress-card {
  margin-bottom: 24px;
}

.errors-section {
  margin-top: 16px;
}

.results-card {
  margin-bottom: 24px;
}

.stats-row {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.devices-table {
  margin-top: 16px;
}
</style>

export interface Alert {
  id: number
  title: string
  description?: string
  alert_type: AlertType
  severity: AlertSeverity
  status: AlertStatus
  
  // Device Association
  device_id?: number
  device_ip?: string
  
  // Alert Details
  source?: string
  rule_id?: string
  threshold_value?: string
  current_value?: string
  
  // Timing
  first_occurrence?: string
  last_occurrence?: string
  resolved_at?: string
  acknowledged_at?: string
  
  // Assignment and Resolution
  assigned_to?: string
  acknowledged_by?: string
  resolved_by?: string
  resolution_notes?: string
  
  // Additional Data
  metadata?: Record<string, any>
  tags?: string[]
  
  // Timestamps
  created_at: string
  updated_at: string
  is_active: boolean
}

export enum AlertType {
  DEVICE_DOWN = 'device_down',
  HIGH_CPU = 'high_cpu',
  HIGH_MEMORY = 'high_memory',
  HIGH_DISK = 'high_disk',
  INTERFACE_DOWN = 'interface_down',
  SECURITY_THREAT = 'security_threat',
  NETWORK_ANOMALY = 'network_anomaly',
  CUSTOM = 'custom'
}

export enum AlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum AlertStatus {
  OPEN = 'open',
  ACKNOWLEDGED = 'acknowledged',
  RESOLVED = 'resolved',
  CLOSED = 'closed'
}

export interface AlertStats {
  total_alerts: number
  open_alerts: number
  critical_alerts: number
}

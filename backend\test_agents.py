#!/usr/bin/env python3
"""
测试新的Agent系统
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from app.core.config import settings

async def test_agents():
    """测试所有Agent"""
    logger.info("开始测试Agent系统...")
    
    try:
        # 导入Agent模块
        from app.agents import (
            general_assistant,
            security_analyst,
            performance_analyst,
            get_available_agents,
            chat_with_agent,
            test_agent_connections
        )
        
        # 1. 测试Agent列表
        logger.info("1. 测试获取可用Agent列表...")
        agents_list = get_available_agents()
        logger.info(f"可用Agent: {agents_list}")
        
        # 2. 测试Agent连接
        logger.info("2. 测试Agent连接...")
        connection_results = await test_agent_connections()
        for agent_type, result in connection_results.items():
            status = "✅" if result.get('status') == 'success' else "❌"
            logger.info(f"{status} {agent_type}: {result.get('status', 'unknown')}")
        
        # 3. 测试通用助手
        logger.info("3. 测试通用助手...")
        general_result = await chat_with_agent(
            agent_type='general',
            message="请提供当前网络监控系统的状态概览"
        )
        logger.info(f"通用助手响应: {general_result.get('response', 'No response')[:100]}...")
        
        # 4. 测试安全专家
        logger.info("4. 测试安全专家...")
        security_result = await chat_with_agent(
            agent_type='security',
            message="请检查IP地址*************的安全状态"
        )
        logger.info(f"安全专家响应: {security_result.get('response', 'No response')[:100]}...")
        
        # 5. 测试性能专家
        logger.info("5. 测试性能专家...")
        performance_result = await chat_with_agent(
            agent_type='performance',
            message="请分析当前系统的性能状况"
        )
        logger.info(f"性能专家响应: {performance_result.get('response', 'No response')[:100]}...")
        
        # 6. 测试错误处理
        logger.info("6. 测试错误处理...")
        error_result = await chat_with_agent(
            agent_type='nonexistent',
            message="测试不存在的Agent"
        )
        logger.info(f"错误处理结果: {error_result.get('error', 'No error')}")
        
        logger.info("✅ Agent系统测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ Agent系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_individual_agents():
    """单独测试每个Agent实例"""
    logger.info("开始单独测试Agent实例...")
    
    try:
        from app.agents.general_assistant import general_assistant
        from app.agents.security_analyst import security_analyst
        from app.agents.performance_analyst import performance_analyst
        
        # 测试通用助手实例
        logger.info("测试通用助手实例...")
        general_response = await general_assistant.chat("你好，请介绍一下你的功能")
        logger.info(f"通用助手: {general_response.get('success', False)}")
        
        # 测试安全专家实例
        logger.info("测试安全专家实例...")
        security_response = await security_analyst.chat("请分析当前的安全威胁情况")
        logger.info(f"安全专家: {security_response.get('success', False)}")
        
        # 测试性能专家实例
        logger.info("测试性能专家实例...")
        performance_response = await performance_analyst.chat("请提供性能优化建议")
        logger.info(f"性能专家: {performance_response.get('success', False)}")
        
        logger.info("✅ 单独Agent实例测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 单独Agent实例测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_environment():
    """检查环境配置"""
    logger.info("检查环境配置...")
    
    required_vars = [
        'DEEPSEEK_API_KEY',
        'DEEPSEEK_BASE_URL',
        'DEEPSEEK_MODEL'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = getattr(settings, var, None)
        if not value:
            missing_vars.append(var)
        else:
            logger.info(f"✅ {var}: {'*' * (len(str(value)) - 4) + str(value)[-4:]}")
    
    if missing_vars:
        logger.error(f"❌ 缺少环境变量: {missing_vars}")
        return False
    
    logger.info("✅ 环境配置检查通过")
    return True

async def main():
    """主测试函数"""
    logger.info("🚀 开始Agent系统重构验证...")
    
    # 检查环境
    if not check_environment():
        logger.error("环境配置检查失败，退出测试")
        return
    
    # 测试Agent系统
    success1 = await test_agents()
    success2 = await test_individual_agents()
    
    if success1 and success2:
        logger.info("🎉 所有测试通过！Agent系统重构成功！")
    else:
        logger.error("❌ 部分测试失败，需要检查配置")

if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 运行测试
    asyncio.run(main())

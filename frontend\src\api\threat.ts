import request from './request'

// 威胁情报相关接口类型定义
export interface IPReputationRequest {
  ip_address: string
}

export interface IPReputationResponse {
  ip_address: string
  is_malicious: boolean
  threat_level: string
  reason?: string
  categories?: string[]
  source: string
  confidence: number
  last_seen?: string
  error?: string
}

export interface BlacklistItem {
  ip_address: string
  reason: string
  severity: string
  source: string
  added_by?: string
  added_at: string
}

export interface BlacklistRequest {
  ip_address: string
  reason: string
  severity: string
}

export interface ThreatStatistics {
  total_threats: number
  high_severity_threats: number
  blacklist_entries: number
  builtin_blacklist_count: number
  last_update?: string
  error?: string
}

export interface ThreatIntelligenceItem {
  id: number
  ip_address: string
  domain?: string
  is_malicious: boolean
  threat_level: string
  categories: string[]
  description?: string
  source: string
  confidence_score: number
  first_seen?: string
  last_seen?: string
  created_at: string
}

export interface BatchIPCheckRequest {
  ip_addresses: string[]
}

export interface BatchIPCheckResponse {
  results: Record<string, IPReputationResponse>
  total_checked: number
  timestamp: string
}

// 威胁情报API
export const threatApi = {
  /**
   * 检查单个IP地址信誉
   */
  checkIpReputation(data: IPReputationRequest) {
    return request.post<IPReputationResponse>('/threat/check-ip', data)
  },

  /**
   * 批量检查IP地址信誉
   */
  batchCheckIps(ipAddresses: string[]) {
    return request.post<BatchIPCheckResponse>('/threat/batch-check-ips', ipAddresses)
  },

  /**
   * 获取IP黑名单
   */
  getBlacklist(params?: {
    skip?: number
    limit?: number
  }) {
    return request.get<{
      items: BlacklistItem[]
      total: number
      skip: number
      limit: number
    }>('/threat/blacklist', { params })
  },

  /**
   * 添加IP到黑名单
   */
  addToBlacklist(data: BlacklistRequest) {
    return request.post<{
      success: boolean
      message: string
      timestamp: string
    }>('/threat/blacklist/add', data)
  },

  /**
   * 从黑名单移除IP
   */
  removeFromBlacklist(ipAddress: string) {
    return request.delete<{
      success: boolean
      message: string
      timestamp: string
    }>(`/threat/blacklist/${ipAddress}`)
  },

  /**
   * 获取威胁统计信息
   */
  getThreatStatistics() {
    return request.get<ThreatStatistics>('/threat/statistics')
  },

  /**
   * 更新威胁情报数据
   */
  updateThreatIntelligence() {
    return request.post<{
      success: boolean
      message: string
      timestamp: string
    }>('/threat/update')
  },

  /**
   * 获取威胁情报数据
   */
  getThreatIntelligence(params?: {
    ip_address?: string
    threat_level?: string
    skip?: number
    limit?: number
  }) {
    return request.get<{
      items: ThreatIntelligenceItem[]
      total: number
      skip: number
      limit: number
    }>('/threat/intelligence', { params })
  }
}

// 威胁级别映射
export const THREAT_LEVELS = {
  safe: { label: '安全', color: 'success', icon: '✅' },
  low: { label: '低危', color: 'info', icon: '🟢' },
  medium: { label: '中危', color: 'warning', icon: '🟡' },
  high: { label: '高危', color: 'error', icon: '🟠' },
  critical: { label: '严重', color: 'error', icon: '🔴' }
}

// 威胁严重程度映射
export const SEVERITY_LEVELS = {
  low: { label: '低', color: 'info' },
  medium: { label: '中', color: 'warning' },
  high: { label: '高', color: 'error' },
  critical: { label: '严重', color: 'error' }
}

// 工具函数
export const threatUtils = {
  /**
   * 获取威胁级别的显示信息
   */
  getThreatLevelInfo(level: string) {
    return THREAT_LEVELS[level as keyof typeof THREAT_LEVELS] || THREAT_LEVELS.safe
  },

  /**
   * 获取严重程度的显示信息
   */
  getSeverityInfo(severity: string) {
    return SEVERITY_LEVELS[severity as keyof typeof SEVERITY_LEVELS] || SEVERITY_LEVELS.low
  },

  /**
   * 验证IP地址格式
   */
  validateIP(ip: string): boolean {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
    return ipv4Regex.test(ip) || ipv6Regex.test(ip)
  },

  /**
   * 解析IP地址列表（支持换行、逗号、空格分隔）
   */
  parseIPList(input: string): string[] {
    return input
      .split(/[\n,\s]+/)
      .map(ip => ip.trim())
      .filter(ip => ip && this.validateIP(ip))
  },

  /**
   * 格式化时间戳
   */
  formatTimestamp(timestamp?: string): string {
    if (!timestamp) return '-'
    return new Date(timestamp).toLocaleString('zh-CN')
  }
}

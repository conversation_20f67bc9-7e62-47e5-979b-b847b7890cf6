# CampusGuard开发环境配置

## 环境要求
- **Python**：3.13.2 + venv虚拟环境
- **Node.js**：20.19+
- **数据库**：MySQL 8.0+
- **包管理器**：pip + requirements.txt (后端), npm/yarn/pnpm (前端)

## 关键环境变量
```bash
# 数据库配置
DATABASE_URL=mysql+pymysql://campusguard:your_password@localhost:3306/campusguard
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=campusguard
MYSQL_USER=campusguard
MYSQL_PASSWORD=your_password

# DeepSeek API配置
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat
MODEL_TIMEOUT=60

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
APP_DEBUG=false

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/campusguard.log
LOG_ROTATION=10MB
LOG_RETENTION=30
```

## 项目结构
- **PRD文档**：CampusGuard_PRD.md (2751行完整技术文档)
- **后端代码**：Python FastAPI应用
- **前端代码**：Vue.js应用
- **数据库**：MySQL数据库设计
- **配置文件**：环境变量和应用配置
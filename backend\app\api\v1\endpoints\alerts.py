"""
Alert management endpoints
"""
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from loguru import logger

from ....core.database import get_db
from ....models.alert import <PERSON><PERSON>, <PERSON>ert<PERSON>everity, AlertStatus, AlertType

router = APIRouter()


@router.get("/")
async def get_alerts(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    severity: Optional[AlertSeverity] = None,
    status: Optional[AlertStatus] = None,
    alert_type: Optional[AlertType] = None,
    device_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """Get alerts with optional filtering"""
    try:
        query = db.query(Alert).filter(Alert.is_active == True)
        
        if severity:
            query = query.filter(Alert.severity == severity)
        if status:
            query = query.filter(Alert.status == status)
        if alert_type:
            query = query.filter(Alert.alert_type == alert_type)
        if device_id:
            query = query.filter(Alert.device_id == device_id)
            
        alerts = query.order_by(Alert.created_at.desc()).offset(skip).limit(limit).all()
        return alerts
        
    except Exception as e:
        logger.error(f"Error getting alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve alerts")


@router.get("/stats")
async def get_alert_stats(db: Session = Depends(get_db)):
    """Get alert statistics"""
    try:
        total_alerts = db.query(Alert).filter(Alert.is_active == True).count()
        open_alerts = db.query(Alert).filter(
            Alert.is_active == True,
            Alert.status == AlertStatus.OPEN
        ).count()
        
        critical_alerts = db.query(Alert).filter(
            Alert.is_active == True,
            Alert.severity == AlertSeverity.CRITICAL,
            Alert.status.in_([AlertStatus.OPEN, AlertStatus.ACKNOWLEDGED])
        ).count()
        
        return {
            "total_alerts": total_alerts,
            "open_alerts": open_alerts,
            "critical_alerts": critical_alerts
        }
        
    except Exception as e:
        logger.error(f"Error getting alert stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve alert statistics")


@router.put("/{alert_id}/acknowledge")
async def acknowledge_alert(alert_id: int, db: Session = Depends(get_db)):
    """Acknowledge an alert"""
    try:
        alert = db.query(Alert).filter(
            Alert.id == alert_id,
            Alert.is_active == True
        ).first()
        
        if not alert:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        alert.status = AlertStatus.ACKNOWLEDGED
        alert.acknowledged_at = datetime.now().isoformat()
        db.commit()
        
        logger.info(f"Alert {alert_id} acknowledged")
        return {"message": "Alert acknowledged successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error acknowledging alert {alert_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to acknowledge alert")


@router.put("/{alert_id}/resolve")
async def resolve_alert(alert_id: int, resolution_notes: str = "", db: Session = Depends(get_db)):
    """Resolve an alert"""
    try:
        alert = db.query(Alert).filter(
            Alert.id == alert_id,
            Alert.is_active == True
        ).first()
        
        if not alert:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        alert.status = AlertStatus.RESOLVED
        alert.resolved_at = datetime.now().isoformat()
        alert.resolution_notes = resolution_notes
        db.commit()
        
        logger.info(f"Alert {alert_id} resolved")
        return {"message": "Alert resolved successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resolving alert {alert_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to resolve alert")


@router.post("/")
async def create_alert(
    title: str,
    description: str,
    alert_type: AlertType,
    severity: AlertSeverity,
    device_id: Optional[int] = None,
    device_ip: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Create new alert"""
    try:
        from ....services.alert_service import AlertService

        alert_service = AlertService()
        alert = await alert_service.create_alert(
            title=title,
            description=description,
            alert_type=alert_type,
            severity=severity,
            device_id=device_id,
            device_ip=device_ip,
            db=db
        )

        return alert

    except Exception as e:
        logger.error(f"Error creating alert: {e}")
        raise HTTPException(status_code=500, detail="Failed to create alert")


@router.get("/statistics")
async def get_alert_statistics(hours: int = 24, db: Session = Depends(get_db)):
    """Get alert statistics"""
    try:
        from ....services.alert_service import AlertService

        alert_service = AlertService()
        stats = await alert_service.get_alert_statistics(db, hours)

        return stats

    except Exception as e:
        logger.error(f"Error getting alert statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve alert statistics")


@router.post("/escalate")
async def escalate_alerts(db: Session = Depends(get_db)):
    """Manually trigger alert escalation"""
    try:
        from ....services.alert_service import AlertService

        alert_service = AlertService()
        await alert_service.escalate_alerts(db)

        return {"message": "Alert escalation completed"}

    except Exception as e:
        logger.error(f"Error escalating alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to escalate alerts")


@router.delete("/cleanup")
async def cleanup_old_alerts(days: int = 30, db: Session = Depends(get_db)):
    """Clean up old resolved alerts"""
    try:
        from ....services.alert_service import AlertService

        alert_service = AlertService()
        await alert_service.cleanup_old_alerts(db, days)

        return {"message": f"Cleaned up alerts older than {days} days"}

    except Exception as e:
        logger.error(f"Error cleaning up alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to clean up alerts")

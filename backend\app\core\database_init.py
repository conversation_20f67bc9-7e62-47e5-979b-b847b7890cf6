"""
Database initialization and sample data creation
"""
from datetime import datetime
from sqlalchemy.orm import Session
from loguru import logger

from .database import SessionLocal, engine
from ..models.base import Base
from ..models.device import Device, DeviceType, DeviceStatus
from ..models.alert import Alert, <PERSON><PERSON><PERSON>everity, AlertStatus, AlertType
from ..models.performance import PerformanceMetric, NetworkTopology, NetworkConnection
from ..models.ai_conversation import Conversation, Message, AnalysisResult, ConversationStatus, MessageRole, AnalysisType


def create_sample_devices(db: Session) -> None:
    """Create sample devices for testing"""
    sample_devices = [
        {
            "name": "Core Switch 01",
            "ip_address": "***********",
            "mac_address": "00:1B:44:11:3A:B7",
            "device_type": DeviceType.SWITCH,
            "vendor": "Cisco",
            "model": "Catalyst 9300",
            "description": "Main core switch for building A",
            "location": "Building A - Server Room",
            "building": "Building A",
            "floor": "1F",
            "room": "Server Room",
            "subnet": "***********/24",
            "vlan_id": 100,
            "status": DeviceStatus.ONLINE,
            "cpu_usage": 15.5,
            "memory_usage": 32.1,
            "disk_usage": 45.2
        },
        {
            "name": "Access Point 01",
            "ip_address": "***********0",
            "mac_address": "00:1B:44:11:3A:C8",
            "device_type": DeviceType.ACCESS_POINT,
            "vendor": "Ubiquiti",
            "model": "UniFi AP AC Pro",
            "description": "WiFi access point for lobby area",
            "location": "Building A - Lobby",
            "building": "Building A",
            "floor": "1F",
            "room": "Lobby",
            "subnet": "***********/24",
            "vlan_id": 200,
            "status": DeviceStatus.ONLINE,
            "cpu_usage": 8.2,
            "memory_usage": 28.5
        },
        {
            "name": "Firewall 01",
            "ip_address": "*************",
            "mac_address": "00:1B:44:11:3A:D9",
            "device_type": DeviceType.FIREWALL,
            "vendor": "Fortinet",
            "model": "FortiGate 100F",
            "description": "Main network firewall",
            "location": "Building A - Server Room",
            "building": "Building A",
            "floor": "1F",
            "room": "Server Room",
            "subnet": "***********/24",
            "status": DeviceStatus.WARNING,
            "cpu_usage": 65.8,
            "memory_usage": 78.3,
            "disk_usage": 23.1
        },
        {
            "name": "Server 01",
            "ip_address": "*************",
            "mac_address": "00:1B:44:11:3A:EA",
            "device_type": DeviceType.SERVER,
            "vendor": "Dell",
            "model": "PowerEdge R740",
            "description": "Main application server",
            "location": "Building A - Server Room",
            "building": "Building A",
            "floor": "1F",
            "room": "Server Room",
            "subnet": "***********/24",
            "status": DeviceStatus.ONLINE,
            "cpu_usage": 42.3,
            "memory_usage": 67.8,
            "disk_usage": 89.2
        }
    ]
    
    for device_data in sample_devices:
        # Check if device already exists
        existing = db.query(Device).filter(Device.ip_address == device_data["ip_address"]).first()
        if not existing:
            device = Device(**device_data)
            db.add(device)
            logger.info(f"Created sample device: {device_data['name']}")
    
    db.commit()


def create_sample_alerts(db: Session) -> None:
    """Create sample alerts for testing"""
    devices = db.query(Device).all()
    if not devices:
        return
    
    sample_alerts = [
        {
            "title": "High CPU Usage Detected",
            "description": "CPU usage has exceeded 60% threshold",
            "alert_type": AlertType.HIGH_CPU,
            "severity": AlertSeverity.HIGH,
            "status": AlertStatus.OPEN,
            "device_id": devices[2].id,  # Firewall
            "device_ip": devices[2].ip_address,
            "source": "SNMP Monitor",
            "threshold_value": "60%",
            "current_value": "65.8%",
            "first_occurrence": datetime.now().isoformat(),
            "last_occurrence": datetime.now().isoformat()
        },
        {
            "title": "High Disk Usage Warning",
            "description": "Disk usage approaching capacity limit",
            "alert_type": AlertType.HIGH_DISK,
            "severity": AlertSeverity.MEDIUM,
            "status": AlertStatus.ACKNOWLEDGED,
            "device_id": devices[3].id,  # Server
            "device_ip": devices[3].ip_address,
            "source": "SNMP Monitor",
            "threshold_value": "85%",
            "current_value": "89.2%",
            "first_occurrence": datetime.now().isoformat(),
            "last_occurrence": datetime.now().isoformat(),
            "acknowledged_at": datetime.now().isoformat(),
            "acknowledged_by": "admin"
        }
    ]
    
    for alert_data in sample_alerts:
        alert = Alert(**alert_data)
        db.add(alert)
        logger.info(f"Created sample alert: {alert_data['title']}")
    
    db.commit()


def create_sample_topology(db: Session) -> None:
    """Create sample network topology for testing"""
    devices = db.query(Device).all()
    if not devices:
        return
    
    # Create topology nodes
    nodes = []
    for i, device in enumerate(devices):
        node = NetworkTopology(
            node_id=f"device_{device.id}",
            node_type="device",
            node_name=device.name,
            node_ip=device.ip_address,
            x_position=100 + (i * 200),
            y_position=100 + (i % 2 * 150),
            properties={
                "device_type": device.device_type.value,
                "vendor": device.vendor,
                "model": device.model,
                "status": device.status.value
            },
            style={
                "color": "#1890ff" if device.status == DeviceStatus.ONLINE else "#ff4d4f",
                "size": 40
            }
        )
        nodes.append(node)
        db.add(node)
    
    db.commit()
    
    # Create connections between devices
    if len(devices) >= 2:
        # Connect core switch to other devices
        core_switch = devices[0]  # Assuming first device is core switch
        
        for device in devices[1:]:
            connection = NetworkConnection(
                source_node_id=f"device_{core_switch.id}",
                target_node_id=f"device_{device.id}",
                connection_type="ethernet",
                bandwidth=1000.0,  # 1Gbps
                latency=1.2,
                packet_loss=0.01,
                utilization=25.5,
                status="active",
                properties={
                    "interface_type": "GigabitEthernet",
                    "duplex": "full"
                },
                style={
                    "color": "#52c41a",
                    "width": 2
                }
            )
            db.add(connection)
            logger.info(f"Created connection: {core_switch.name} -> {device.name}")
    
    db.commit()


def init_sample_data():
    """Initialize database with sample data"""
    try:
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        
        # Create sample data
        with SessionLocal() as db:
            logger.info("Creating sample data...")
            
            create_sample_devices(db)
            create_sample_alerts(db)
            create_sample_topology(db)
            
            logger.info("Sample data created successfully")
            
    except Exception as e:
        logger.error(f"Failed to initialize sample data: {e}")
        raise


if __name__ == "__main__":
    init_sample_data()

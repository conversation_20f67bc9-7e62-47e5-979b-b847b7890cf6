"""
Monitoring management endpoints
"""
from typing import Optional
from fastapi import APIRouter, HTTPException, Query
from loguru import logger

from ....services.monitoring_service import monitoring_service

router = APIRouter()


@router.get("/status")
async def get_monitoring_status():
    """Get monitoring service status"""
    try:
        status = await monitoring_service.get_monitoring_status()
        return status
        
    except Exception as e:
        logger.error(f"Error getting monitoring status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get monitoring status")


@router.post("/start")
async def start_monitoring():
    """Start monitoring service"""
    try:
        await monitoring_service.start_monitoring()
        return {"message": "Monitoring service started successfully"}
        
    except Exception as e:
        logger.error(f"Error starting monitoring service: {e}")
        raise HTTPException(status_code=500, detail="Failed to start monitoring service")


@router.post("/stop")
async def stop_monitoring():
    """Stop monitoring service"""
    try:
        await monitoring_service.stop_monitoring()
        return {"message": "Monitoring service stopped successfully"}
        
    except Exception as e:
        logger.error(f"Error stopping monitoring service: {e}")
        raise HTTPException(status_code=500, detail="Failed to stop monitoring service")


@router.post("/discover")
async def discover_devices(ip_range: str = Query(..., description="IP range to scan (e.g., ***********/24)")):
    """Trigger device discovery"""
    try:
        result = await monitoring_service.trigger_device_discovery(ip_range)
        return result
        
    except Exception as e:
        logger.error(f"Error in device discovery: {e}")
        raise HTTPException(status_code=500, detail="Failed to discover devices")


@router.post("/monitor-now")
async def trigger_monitoring():
    """Manually trigger device monitoring cycle"""
    try:
        from ....services.device_service import DeviceService
        
        device_service = DeviceService()
        result = await device_service.monitor_all_devices()
        
        return {
            "message": "Manual monitoring cycle completed",
            "result": result
        }
        
    except Exception as e:
        logger.error(f"Error in manual monitoring: {e}")
        raise HTTPException(status_code=500, detail="Failed to trigger monitoring")

"""
Performance Analyst Agent - 网络性能分析专家
"""
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from loguru import logger
from sqlalchemy import func, desc

from agents import Agent, Runner, function_tool, set_tracing_disabled
from agents.extensions.models.litellm_model import LitellmModel

from ..core.config import settings
from ..core.database import SessionLocal
from ..models.device import Device, DeviceStatus
from ..models.performance import PerformanceMetric
from ..models.alert import Alert, AlertType, AlertSeverity

# 禁用追踪功能
set_tracing_disabled(True)


@function_tool
async def get_performance_overview(hours: int = 24) -> str:
    """获取性能概览
    
    Args:
        hours: 查询最近多少小时的数据（默认24小时）
    
    Returns:
        性能概览信息
    """
    try:
        with SessionLocal() as db:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            # 获取性能指标统计
            metrics = db.query(PerformanceMetric).filter(
                PerformanceMetric.is_active == True,
                PerformanceMetric.timestamp >= start_time
            ).all()
            
            if not metrics:
                return f"最近{hours}小时内没有性能数据"
            
            # 按指标类型分组统计
            cpu_metrics = [m for m in metrics if 'cpu' in m.metric_name.lower()]
            memory_metrics = [m for m in metrics if 'memory' in m.metric_name.lower()]
            network_metrics = [m for m in metrics if any(kw in m.metric_name.lower() for kw in ['network', 'bandwidth', 'traffic'])]
            
            result = f"📊 最近{hours}小时性能概览：\n"
            result += f"总指标数：{len(metrics)}\n"
            
            if cpu_metrics:
                avg_cpu = sum(float(m.metric_value) for m in cpu_metrics) / len(cpu_metrics)
                max_cpu = max(float(m.metric_value) for m in cpu_metrics)
                result += f"CPU使用率：平均{avg_cpu:.1f}%，峰值{max_cpu:.1f}%\n"
            
            if memory_metrics:
                avg_memory = sum(float(m.metric_value) for m in memory_metrics) / len(memory_metrics)
                max_memory = max(float(m.metric_value) for m in memory_metrics)
                result += f"内存使用率：平均{avg_memory:.1f}%，峰值{max_memory:.1f}%\n"
            
            if network_metrics:
                result += f"网络指标：{len(network_metrics)}个数据点\n"
            
            # 获取性能告警
            perf_alerts = db.query(Alert).filter(
                Alert.is_active == True,
                Alert.created_at >= start_time,
                Alert.alert_type.in_([AlertType.HIGH_CPU, AlertType.HIGH_MEMORY, AlertType.HIGH_BANDWIDTH])
            ).count()
            
            if perf_alerts > 0:
                result += f"⚠️ 性能相关告警：{perf_alerts}个"
            else:
                result += "✅ 无性能告警"
            
            return result
            
    except Exception as e:
        logger.error(f"获取性能概览失败: {e}")
        return f"获取性能概览失败：{str(e)}"


@function_tool
async def analyze_device_performance(device_id: int, hours: int = 24) -> str:
    """分析特定设备的性能
    
    Args:
        device_id: 设备ID
        hours: 分析最近多少小时的数据（默认24小时）
    
    Returns:
        设备性能分析结果
    """
    try:
        with SessionLocal() as db:
            # 获取设备信息
            device = db.query(Device).filter(Device.id == device_id).first()
            if not device:
                return f"未找到ID为{device_id}的设备"
            
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            # 获取设备性能指标
            metrics = db.query(PerformanceMetric).filter(
                PerformanceMetric.device_id == device_id,
                PerformanceMetric.timestamp >= start_time,
                PerformanceMetric.is_active == True
            ).order_by(PerformanceMetric.timestamp.desc()).all()
            
            if not metrics:
                return f"设备 {device.name} 最近{hours}小时内没有性能数据"
            
            result = f"🔍 设备 {device.name} ({device.ip_address}) 性能分析：\n"
            
            # 按指标类型分析
            metric_types = {}
            for metric in metrics:
                metric_type = metric.metric_name
                if metric_type not in metric_types:
                    metric_types[metric_type] = []
                metric_types[metric_type].append(float(metric.metric_value))
            
            for metric_type, values in metric_types.items():
                if values:
                    avg_val = sum(values) / len(values)
                    max_val = max(values)
                    min_val = min(values)
                    latest_val = values[0]  # 最新值（因为按时间倒序）
                    
                    # 判断趋势
                    if len(values) >= 2:
                        recent_avg = sum(values[:len(values)//4]) / (len(values)//4) if len(values) >= 4 else values[0]
                        older_avg = sum(values[len(values)//2:]) / (len(values) - len(values)//2)
                        trend = "📈 上升" if recent_avg > older_avg * 1.1 else "📉 下降" if recent_avg < older_avg * 0.9 else "➡️ 稳定"
                    else:
                        trend = "➡️ 数据不足"
                    
                    result += f"\n{metric_type}:\n"
                    result += f"  当前值: {latest_val:.2f}\n"
                    result += f"  平均值: {avg_val:.2f}\n"
                    result += f"  最大值: {max_val:.2f}\n"
                    result += f"  最小值: {min_val:.2f}\n"
                    result += f"  趋势: {trend}\n"
            
            # 检查相关告警
            alerts = db.query(Alert).filter(
                Alert.device_id == device_id,
                Alert.created_at >= start_time,
                Alert.is_active == True
            ).count()
            
            if alerts > 0:
                result += f"\n⚠️ 相关告警：{alerts}个"
            else:
                result += f"\n✅ 无相关告警"
            
            return result
            
    except Exception as e:
        logger.error(f"分析设备性能失败: {e}")
        return f"分析设备性能失败：{str(e)}"


@function_tool
async def get_top_performance_issues(limit: int = 10) -> str:
    """获取性能问题排行
    
    Args:
        limit: 返回的问题数量限制（默认10个）
    
    Returns:
        性能问题排行信息
    """
    try:
        with SessionLocal() as db:
            # 获取最近24小时的高CPU使用率设备
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)
            
            # 查询CPU使用率高的设备
            high_cpu_devices = db.query(Device).filter(
                Device.is_active == True,
                Device.cpu_usage > 80
            ).order_by(desc(Device.cpu_usage)).limit(limit).all()
            
            # 查询内存使用率高的设备
            high_memory_devices = db.query(Device).filter(
                Device.is_active == True,
                Device.memory_usage > 80
            ).order_by(desc(Device.memory_usage)).limit(limit).all()
            
            # 查询最近的性能告警
            perf_alerts = db.query(Alert).filter(
                Alert.is_active == True,
                Alert.created_at >= start_time,
                Alert.alert_type.in_([
                    AlertType.HIGH_CPU,
                    AlertType.HIGH_MEMORY,
                    AlertType.HIGH_BANDWIDTH,
                    AlertType.DEVICE_OVERLOAD
                ])
            ).order_by(desc(Alert.created_at)).limit(limit).all()
            
            result = "🚨 性能问题排行：\n"
            
            if high_cpu_devices:
                result += f"\n💻 CPU使用率过高（>80%）：\n"
                for i, device in enumerate(high_cpu_devices, 1):
                    result += f"{i}. {device.name} ({device.ip_address}): {device.cpu_usage or 0}%\n"
            
            if high_memory_devices:
                result += f"\n🧠 内存使用率过高（>80%）：\n"
                for i, device in enumerate(high_memory_devices, 1):
                    result += f"{i}. {device.name} ({device.ip_address}): {device.memory_usage or 0}%\n"
            
            if perf_alerts:
                result += f"\n⚠️ 最近性能告警：\n"
                for i, alert in enumerate(perf_alerts, 1):
                    severity_icon = "🔴" if alert.severity == AlertSeverity.CRITICAL else "🟠" if alert.severity == AlertSeverity.HIGH else "🟡"
                    result += f"{i}. {severity_icon} {alert.title} - {alert.device_ip or 'N/A'} ({alert.created_at.strftime('%m-%d %H:%M')})\n"
            
            if not high_cpu_devices and not high_memory_devices and not perf_alerts:
                result += "✅ 当前没有明显的性能问题"
            
            return result
            
    except Exception as e:
        logger.error(f"获取性能问题排行失败: {e}")
        return f"获取性能问题排行失败：{str(e)}"


@function_tool
async def recommend_performance_optimization(metric_type: str, current_value: float) -> str:
    """推荐性能优化建议
    
    Args:
        metric_type: 指标类型（如cpu_usage, memory_usage等）
        current_value: 当前值
    
    Returns:
        性能优化建议
    """
    try:
        recommendations = []
        
        if "cpu" in metric_type.lower():
            if current_value > 90:
                recommendations = [
                    "🚨 CPU使用率极高，建议立即采取措施：",
                    "1. 检查是否有异常进程占用大量CPU",
                    "2. 考虑重启高负载服务",
                    "3. 检查是否有死循环或无限递归",
                    "4. 考虑增加CPU资源或负载均衡"
                ]
            elif current_value > 80:
                recommendations = [
                    "⚠️ CPU使用率较高，建议优化：",
                    "1. 监控CPU使用率趋势",
                    "2. 优化高耗CPU的应用程序",
                    "3. 考虑任务调度优化",
                    "4. 评估是否需要扩容"
                ]
            elif current_value > 70:
                recommendations = [
                    "💡 CPU使用率偏高，预防性建议：",
                    "1. 定期监控CPU使用模式",
                    "2. 优化应用程序性能",
                    "3. 考虑预防性扩容计划"
                ]
            else:
                recommendations = ["✅ CPU使用率正常，保持当前配置"]
        
        elif "memory" in metric_type.lower():
            if current_value > 95:
                recommendations = [
                    "🚨 内存使用率极高，紧急处理：",
                    "1. 立即检查内存泄漏",
                    "2. 重启内存占用高的服务",
                    "3. 清理临时文件和缓存",
                    "4. 考虑增加内存容量"
                ]
            elif current_value > 85:
                recommendations = [
                    "⚠️ 内存使用率较高，建议优化：",
                    "1. 分析内存使用模式",
                    "2. 优化应用程序内存管理",
                    "3. 调整缓存策略",
                    "4. 考虑内存扩容"
                ]
            elif current_value > 75:
                recommendations = [
                    "💡 内存使用率偏高，预防性建议：",
                    "1. 监控内存使用趋势",
                    "2. 定期清理不必要的缓存",
                    "3. 优化数据结构使用"
                ]
            else:
                recommendations = ["✅ 内存使用率正常，保持当前配置"]
        
        elif "network" in metric_type.lower() or "bandwidth" in metric_type.lower():
            if current_value > 90:
                recommendations = [
                    "🚨 网络带宽使用率极高：",
                    "1. 检查是否有异常流量",
                    "2. 分析网络流量模式",
                    "3. 考虑流量限制或QoS",
                    "4. 评估带宽扩容需求"
                ]
            elif current_value > 80:
                recommendations = [
                    "⚠️ 网络带宽使用率较高：",
                    "1. 监控网络流量趋势",
                    "2. 优化数据传输效率",
                    "3. 考虑负载均衡",
                    "4. 评估网络架构优化"
                ]
            else:
                recommendations = ["✅ 网络使用率正常"]
        
        else:
            recommendations = [
                f"📊 {metric_type} 当前值：{current_value}",
                "建议定期监控该指标的变化趋势，",
                "如发现异常波动请及时分析原因。"
            ]
        
        return "\n".join(recommendations)
        
    except Exception as e:
        logger.error(f"生成性能优化建议失败: {e}")
        return f"生成性能优化建议失败：{str(e)}"


class PerformanceAnalystAgent:
    """网络性能分析专家Agent"""
    
    def __init__(self):
        """初始化性能分析Agent"""
        try:
            # 创建LiteLLM模型实例
            self.model = LitellmModel(
                model=f"deepseek/{settings.DEEPSEEK_MODEL}",
                api_key=settings.DEEPSEEK_API_KEY,
                base_url=settings.DEEPSEEK_BASE_URL
            )
            
            # 创建Agent实例
            self.agent = Agent(
                name="CampusGuard性能专家",
                instructions="""你是CampusGuard网络监控系统的性能分析专家。你的专业领域包括：

1. 网络设备性能监控和分析
2. CPU、内存、网络带宽使用率分析
3. 性能瓶颈识别和诊断
4. 性能优化建议和容量规划
5. 性能趋势分析和预测
6. 系统负载均衡和资源调优

你具备以下专业知识：
- 系统性能监控指标解读
- 性能瓶颈根因分析
- 容量规划和扩容建议
- 性能优化最佳实践
- 负载均衡和资源调度
- 性能基准测试和评估

请用中文回复，使用专业的性能分析术语，提供详细的数据分析和具体的优化建议。对于性能问题，请提供分级的解决方案和预防措施。""",
                model=self.model,
                tools=[
                    get_performance_overview,
                    analyze_device_performance,
                    get_top_performance_issues,
                    recommend_performance_optimization
                ]
            )
            
            logger.info("性能分析Agent初始化成功")
            
        except Exception as e:
            logger.error(f"性能分析Agent初始化失败: {e}")
            raise
    
    async def chat(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """与性能专家对话
        
        Args:
            message: 用户消息
            context: 上下文信息（可选）
        
        Returns:
            包含响应信息的字典
        """
        try:
            logger.info(f"性能专家收到消息: {message}")
            
            # 运行Agent
            result = await Runner.run(
                self.agent,
                input=message,
                max_turns=5
            )
            
            response_data = {
                "agent_name": "性能专家",
                "agent_type": "performance_analyst",
                "response": result.final_output,
                "timestamp": datetime.now().isoformat(),
                "success": True
            }
            
            logger.info("性能专家响应成功")
            return response_data
            
        except Exception as e:
            logger.error(f"性能专家对话失败: {e}")
            return {
                "agent_name": "性能专家",
                "agent_type": "performance_analyst",
                "response": f"抱歉，处理您的性能咨询时发生错误：{str(e)}",
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": str(e)
            }


# 全局性能专家实例
performance_analyst = PerformanceAnalystAgent()
